<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive UHF RFID Warehouse System Simulation</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        :root {
            --primary-blue: #2563eb;
            --primary-green: #059669;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-white: #ffffff;
            --bg-gray-50: #f9fafb;
            --bg-gray-100: #f3f4f6;
            --border-gray: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-white);
            min-height: 100vh;
            line-height: 1.6;
            color: var(--text-primary);
        }

        /* Header Styles */
        .header {
            background: var(--primary-blue);
            color: white;
            padding: 1rem 2rem;
            box-shadow: var(--shadow-md);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .system-status {
            display: flex;
            gap: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
        }

        .status-indicator.warning {
            background: var(--warning-color);
        }

        .status-indicator.danger {
            background: var(--danger-color);
        }

        /* Enhanced Main Layout */
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .top-section {
            width: 100%;
        }

        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .left-panel, .right-panel {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        /* Enhanced Transmission Card */
        .transmission-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 2px solid #475569;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .transmission-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            animation: scanLine 3s infinite;
        }

        .transmission-card .card-title {
            color: white;
            font-size: 1.5rem;
        }

        .transmission-controls {
            display: flex;
            gap: 1rem;
        }

        /* Enhanced Warehouse Card */
        .warehouse-card {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            border: 2px solid #334155;
            color: white;
            position: relative;
        }

        .warehouse-card .card-title {
            color: white;
            font-size: 1.5rem;
        }

        .warehouse-controls {
            display: flex;
            gap: 1rem;
        }

        .warehouse-container {
            display: grid;
            grid-template-columns: 1fr 200px;
            gap: 2rem;
            margin-top: 1rem;
        }

        /* Enhanced Warehouse Map */
        .warehouse-map {
            position: relative;
            height: 600px;
            background: radial-gradient(circle at center, #1e293b, #0f172a);
            border: 3px solid #475569;
            border-radius: 1rem;
            overflow: hidden;
        }

        .signal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.6;
        }

        /* Warehouse Stats */
        .warehouse-stats {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .stat-item {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #60a5fa;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #cbd5e1;
        }

        /* Enhanced Legend */
        .warehouse-legend {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 1.5rem;
            padding: 1.5rem;
            background: rgba(15, 23, 42, 0.5);
            border-radius: 1rem;
            border: 1px solid #334155;
        }

        .legend-group h4 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.125rem;
        }

        .legend-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .legend-dot.metal { background: var(--primary-blue); }
        .legend-dot.non-metal { background: var(--success-color); }
        .legend-dot.violation { background: var(--danger-color); animation: pulse 1s infinite; }

        .signal-strength-bar {
            display: flex;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .strength-segment {
            flex: 1;
        }

        .strength-segment.strong { background: var(--success-color); }
        .strength-segment.medium { background: var(--warning-color); }
        .strength-segment.weak { background: var(--danger-color); }

        .strength-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            color: #cbd5e1;
        }

        /* Card Styles */
        .card {
            background: var(--bg-white);
            border: 1px solid var(--border-gray);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-gray);
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
        }

        /* Button Styles */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        /* Data Transmission Network */
        .network-diagram {
            position: relative;
            height: 400px;
            background: var(--bg-gray-50);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .network-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            border: 3px solid white;
            box-shadow: var(--shadow-md);
        }

        .network-node:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        .node-cm4 {
            background: var(--primary-blue);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .node-r420 {
            background: var(--warning-color);
            top: 30%;
            left: 30%;
            transform: translate(-50%, -50%);
        }

        .node-antenna {
            background: var(--success-color);
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .node-antenna-1 { top: 10%; left: 20%; }
        .node-antenna-2 { top: 10%; right: 20%; }
        .node-antenna-3 { bottom: 10%; left: 20%; }
        .node-antenna-4 { bottom: 10%; right: 20%; }

        .node-poe {
            background: var(--text-secondary);
            top: 70%;
            left: 70%;
            transform: translate(-50%, -50%);
        }

        .node-power {
            background: var(--danger-color);
            bottom: 20%;
            right: 10%;
        }

        .node-ui {
            background: var(--primary-green);
            top: 20%;
            right: 10%;
        }

        .data-flow-line {
            position: absolute;
            height: 3px;
            background: var(--success-color);
            opacity: 0;
            animation: dataFlow 2s infinite;
        }

        .data-flow-line.write {
            background: var(--warning-color);
        }

        @keyframes dataFlow {
            0% { opacity: 0; transform: scaleX(0); }
            50% { opacity: 1; transform: scaleX(1); }
            100% { opacity: 0; transform: scaleX(1); }
        }

        /* Tag Inventory Table */
        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .inventory-table th,
        .inventory-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-gray);
        }

        .inventory-table th {
            background: var(--bg-gray-50);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .inventory-table td {
            font-size: 0.875rem;
        }

        .rssi-indicator {
            display: inline-block;
            width: 60px;
            height: 8px;
            background: var(--bg-gray-100);
            border-radius: 4px;
            overflow: hidden;
        }

        .rssi-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s;
        }

        .rssi-strong { background: var(--success-color); }
        .rssi-medium { background: var(--warning-color); }
        .rssi-weak { background: var(--danger-color); }

        /* Warehouse Map */
        .warehouse-map {
            position: relative;
            height: 500px;
            background: var(--bg-gray-50);
            border: 2px solid var(--border-gray);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .map-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
        }

        .map-boundary {
            position: absolute;
            top: 5%;
            left: 5%;
            width: 90%;
            height: 90%;
            border: 3px dashed var(--warning-color);
            border-radius: 0.5rem;
        }

        .map-tag {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            transition: all 0.3s;
        }

        .map-tag:hover {
            transform: scale(1.5);
        }

        .map-tag.metal { background: var(--primary-blue); }
        .map-tag.non-metal { background: var(--success-color); }
        .map-tag.violation { background: var(--danger-color); animation: pulse 1s infinite; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--border-gray);
            border-radius: 0.375rem;
            font-size: 0.875rem;
            transition: border-color 0.2s;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        /* Settings Panel */
        .settings-panel {
            background: var(--bg-gray-50);
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .power-slider {
            width: 100%;
            margin: 1rem 0;
        }

        .power-value {
            font-weight: 600;
            color: var(--primary-blue);
        }

        /* Alert Styles */
        .alert {
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }

        .alert-success {
            background: #f0fdf4;
            border-color: var(--success-color);
            color: #166534;
        }

        .alert-warning {
            background: #fffbeb;
            border-color: var(--warning-color);
            color: #92400e;
        }

        .alert-danger {
            background: #fef2f2;
            border-color: var(--danger-color);
            color: #991b1b;
        }

        /* Node Details Modal */
        .node-details {
            position: absolute;
            background: white;
            border: 1px solid var(--border-gray);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            min-width: 200px;
            display: none;
        }

        .node-details.show {
            display: block;
        }

        .node-details h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
            font-weight: 600;
        }

        .node-details p {
            margin: 0.25rem 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .system-status {
                justify-content: center;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .network-node {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .node-antenna {
                width: 35px;
                height: 35px;
                font-size: 0.875rem;
            }

            .warehouse-map {
                height: 300px;
            }

            /* Materials section responsive */
            .materials-section {
                margin: 1rem;
                padding: 1.5rem;
            }

            .materials-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-top: 1.5rem;
            }

            .material-card {
                padding: 1.5rem;
            }

            .material-icon {
                width: 60px;
                height: 60px;
                font-size: 1.75rem;
            }

            .material-header {
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .material-info h4 {
                font-size: 1.125rem;
            }

            .material-specs {
                padding: 0.75rem;
                font-size: 0.875rem;
            }

            .material-purpose {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            /* Connection legend responsive */
            .flex.items-center.gap-4 {
                flex-wrap: wrap;
                gap: 0.5rem !important;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .materials-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Enhanced Materials Section */
        .materials-section {
            background: linear-gradient(135deg, var(--bg-white) 0%, #f8fafc 100%);
            border: 1px solid var(--border-gray);
            border-radius: 1rem;
            padding: 2.5rem;
            box-shadow: var(--shadow-lg);
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }

        .materials-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-green), var(--warning-color));
            border-radius: 1rem 1rem 0 0;
        }

        .materials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .material-card {
            background: var(--bg-white);
            border: 2px solid transparent;
            border-radius: 1rem;
            padding: 2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .material-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .material-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-blue);
        }

        .material-card:hover::before {
            opacity: 1;
        }

        .material-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .material-icon {
            width: 70px;
            height: 70px;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            flex-shrink: 0;
            position: relative;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
        }

        .material-icon::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 1rem;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .material-card:hover .material-icon::after {
            opacity: 1;
        }

        .material-icon.processing {
            background: linear-gradient(135deg, var(--primary-blue), #1e40af);
        }
        .material-icon.reader {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }
        .material-icon.antenna {
            background: linear-gradient(135deg, var(--success-color), #047857);
        }
        .material-icon.tag-metal {
            background: linear-gradient(135deg, var(--primary-blue), #1e3a8a);
        }
        .material-icon.tag-nonmetal {
            background: linear-gradient(135deg, var(--primary-green), #047857);
        }
        .material-icon.network {
            background: linear-gradient(135deg, var(--text-secondary), #374151);
        }
        .material-icon.power {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .material-info h4 {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
            line-height: 1.2;
        }

        .material-model {
            font-size: 1rem;
            color: var(--text-secondary);
            margin: 0;
            font-weight: 500;
        }

        .material-qty {
            background: linear-gradient(135deg, var(--primary-blue), #1e40af);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 700;
            display: inline-block;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
            position: relative;
            overflow: hidden;
        }

        .material-qty::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .material-card:hover .material-qty::before {
            left: 100%;
        }

        .material-specs {
            font-size: 0.95rem;
            color: var(--text-primary);
            line-height: 1.6;
            background: var(--bg-gray-50);
            padding: 1rem;
            border-radius: 0.75rem;
            border-left: 4px solid var(--primary-blue);
            margin-bottom: 1rem;
        }

        .material-specs strong {
            color: var(--primary-blue);
            font-weight: 600;
            display: block;
            margin-bottom: 0.5rem;
        }

        .material-purpose {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-top: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border-radius: 0.75rem;
            border-left: 4px solid var(--primary-green);
            font-style: italic;
            line-height: 1.5;
            position: relative;
        }

        .material-purpose::before {
            content: '💡';
            position: absolute;
            top: 0.75rem;
            right: 1rem;
            font-size: 1.25rem;
            opacity: 0.7;
        }

        /* Advanced Data Flow Lines with Enhanced Effects */
        .connection-line {
            position: absolute;
            z-index: 1;
            pointer-events: none;
            border-radius: 3px;
            overflow: hidden;
        }

        .connection-line.rf {
            height: 6px;
            background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
            box-shadow:
                0 0 15px rgba(16, 185, 129, 0.8),
                0 0 30px rgba(16, 185, 129, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            animation: rfPulseAdvanced 1.5s infinite;
        }

        .connection-line.rf::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            animation: shimmer 2s infinite;
        }

        .connection-line.ethernet {
            height: 7px;
            background: linear-gradient(90deg, #2563eb, #3b82f6, #60a5fa);
            box-shadow:
                0 0 12px rgba(37, 99, 235, 0.6),
                0 0 25px rgba(37, 99, 235, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .connection-line.ethernet::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.5);
            transform: translateY(-50%);
        }

        .connection-line.power {
            height: 8px;
            background: linear-gradient(90deg, #dc2626, #f59e0b, #fbbf24);
            box-shadow:
                0 0 18px rgba(239, 68, 68, 0.7),
                0 0 35px rgba(239, 68, 68, 0.3);
            animation: powerFlow 1s infinite;
        }

        .connection-line.data {
            height: 5px;
            background: linear-gradient(90deg, #059669, #10b981, #34d399);
            animation: dataTransmitAdvanced 1s infinite;
            box-shadow:
                0 0 12px rgba(5, 150, 105, 0.8),
                0 0 24px rgba(5, 150, 105, 0.4);
            position: relative;
        }

        .connection-line.data::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            animation: dataPacket 1s infinite;
        }

        /* Data Particles */
        .data-particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #ffffff, var(--primary-green));
            border-radius: 50%;
            box-shadow: 0 0 15px var(--primary-green);
            z-index: 2;
            animation: particleMove 2s linear;
        }

        .data-particle.fast {
            animation: particleMove 1s linear;
            background: radial-gradient(circle, #ffffff, var(--warning-color));
            box-shadow: 0 0 15px var(--warning-color);
        }

        /* Node Pulse Effects */
        .network-node.active {
            animation: nodePulse 1s ease-in-out;
            box-shadow: 0 0 20px rgba(37, 99, 235, 0.6);
        }

        .network-node.transmitting::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid var(--primary-green);
            border-radius: 50%;
            animation: signalRing 1.5s infinite;
        }

        /* Signal Waves from Antennas */
        .signal-wave {
            position: absolute;
            border: 2px solid var(--success-color);
            border-radius: 50%;
            opacity: 0.7;
            animation: signalExpand 3s infinite;
            pointer-events: none;
        }

        /* Advanced Enhanced Animations */
        @keyframes scanLine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes rfPulseAdvanced {
            0% {
                opacity: 0.6;
                transform: scaleY(1) scaleX(0.8);
                box-shadow: 0 0 10px rgba(16, 185, 129, 0.4);
            }
            50% {
                opacity: 1;
                transform: scaleY(1.2) scaleX(1);
                box-shadow: 0 0 25px rgba(16, 185, 129, 1), 0 0 50px rgba(16, 185, 129, 0.5);
            }
            100% {
                opacity: 0.6;
                transform: scaleY(1) scaleX(0.8);
                box-shadow: 0 0 10px rgba(16, 185, 129, 0.4);
            }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes powerFlow {
            0% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
                filter: brightness(1);
            }
            50% {
                box-shadow: 0 0 30px rgba(239, 68, 68, 1), 0 0 60px rgba(245, 158, 11, 0.8);
                filter: brightness(1.3);
            }
            100% {
                box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
                filter: brightness(1);
            }
        }

        @keyframes dataTransmitAdvanced {
            0% {
                opacity: 0.7;
                transform: scaleX(0.9);
                filter: brightness(1);
            }
            50% {
                opacity: 1;
                transform: scaleX(1.1);
                filter: brightness(1.4) saturate(1.2);
            }
            100% {
                opacity: 0.7;
                transform: scaleX(0.9);
                filter: brightness(1);
            }
        }

        @keyframes dataPacket {
            0% { left: -20px; opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { left: 100%; opacity: 0; }
        }

        @keyframes particleMove {
            0% { transform: translate(0, 0) scale(0.5); opacity: 0; }
            10% { opacity: 1; transform: scale(1); }
            90% { opacity: 1; }
            100% { transform: translate(var(--deltaX, 100px), var(--deltaY, 0)) scale(0.5); opacity: 0; }
        }

        @keyframes particleMoveFast {
            0% { transform: translate(0, 0) scale(0.8); opacity: 0; }
            15% { opacity: 1; transform: scale(1.2); }
            85% { opacity: 1; }
            100% { transform: translate(var(--deltaX, 100px), var(--deltaY, 0)) scale(0.8); opacity: 0; }
        }

        @keyframes nodePulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes signalRing {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }

        @keyframes signalExpand {
            0% { transform: scale(0.5); opacity: 0.8; }
            100% { transform: scale(3); opacity: 0; }
        }

        /* Scanning Effects */
        .scanning-indicator {
            position: absolute;
            top: -10px;
            right: -10px;
            width: 20px;
            height: 20px;
            background: var(--success-color);
            border-radius: 50%;
            animation: scanPulse 1s infinite;
        }

        @keyframes scanPulse {
            0%, 100% { transform: scale(0.8); opacity: 0.7; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        /* Loading Indicators */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-gray);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-between { justify-content: space-between; }
        .gap-2 { gap: 0.5rem; }
        .gap-4 { gap: 1rem; }
        .w-full { width: 100%; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1>🏭 UHF RFID Warehouse System Simulation</h1>
            <div class="system-status">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>R420 Connected</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>4 Antennas Active</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span id="uptime">CM4 Uptime: 72h 15m</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Materials Section -->
    <div class="materials-section" style="max-width: 1400px; margin: 2rem auto; padding: 1.5rem 2rem;">
        <div class="card-header">
            <h2 class="card-title">🔧 System Components & Materials</h2>
        </div>
        <div class="materials-grid">
            <!-- Raspberry Pi CM4 -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon processing">🖥️</div>
                    <div class="material-info">
                        <h4>Raspberry Pi CM4</h4>
                        <p class="material-model">Model: CM4104032</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 1</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • ARM Cortex-A72 quad-core 1.5GHz<br>
                    • 4GB LPDDR4 RAM, 32GB eMMC<br>
                    • WiFi 802.11ac, Bluetooth 5.0<br>
                    • GPIO, USB 2.0, PCIe Gen 2<br>
                    • Power: 5V/3A (15W max)
                </div>
                <div class="material-purpose">
                    Central processing unit for RFID system control, data processing, and network communication
                </div>
            </div>

            <!-- Impinj Speedway R420 -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon reader">📡</div>
                    <div class="material-info">
                        <h4>Impinj Speedway R420</h4>
                        <p class="material-model">Model: IPJ-REV-R420</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 1</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • Frequency: 902-928 MHz (US)<br>
                    • 4 antenna ports (TNC connectors)<br>
                    • Max power: 30 dBm (1W) per port<br>
                    • Read range: up to 50m<br>
                    • PoE+ powered (25.5W)
                </div>
                <div class="material-purpose">
                    UHF RFID reader for tag interrogation, data collection, and antenna management
                </div>
            </div>

            <!-- Laird S9028PCL Antennas -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon antenna">📶</div>
                    <div class="material-info">
                        <h4>Laird S9028PCL Antenna</h4>
                        <p class="material-model">Model: S9028PCL</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 4</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • Frequency: 902-928 MHz<br>
                    • Gain: 8 dBi circular polarized<br>
                    • Beamwidth: 70° (3dB)<br>
                    • Connector: TNC female<br>
                    • IP67 rated, outdoor capable
                </div>
                <div class="material-purpose">
                    High-gain antennas for extended read range and reliable tag detection in warehouse environment
                </div>
            </div>

            <!-- Xerafy Dot XS Metal Tags -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon tag-metal">🏷️</div>
                    <div class="material-info">
                        <h4>Xerafy Dot XS Metal Tags</h4>
                        <p class="material-model">Model: XS1001</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 20</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • Frequency: 860-960 MHz<br>
                    • Read range: up to 4m on metal<br>
                    • Size: 12 × 12 × 2.2mm<br>
                    • Memory: 96-bit EPC, 512-bit user<br>
                    • Temperature: -40°C to +85°C
                </div>
                <div class="material-purpose">
                    Specialized tags for metal equipment tracking - drills, welders, machinery, tools
                </div>
            </div>

            <!-- Avery Dennison AD-160u7 Tags -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon tag-nonmetal">🏷️</div>
                    <div class="material-info">
                        <h4>Avery Dennison AD-160u7</h4>
                        <p class="material-model">Model: AD-160u7</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 10</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • Frequency: 860-960 MHz<br>
                    • Read range: up to 8m (free space)<br>
                    • Size: 97 × 27mm<br>
                    • Memory: 128-bit EPC, 512-bit user<br>
                    • Adhesive backing
                </div>
                <div class="material-purpose">
                    General-purpose tags for non-metal items - pallets, containers, boxes, shelving
                </div>
            </div>

            <!-- NETGEAR GS308PP PoE Switch -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon network">🔌</div>
                    <div class="material-info">
                        <h4>NETGEAR GS308PP</h4>
                        <p class="material-model">Model: GS308PP-100NAS</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 1</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • 8 Gigabit Ethernet ports<br>
                    • PoE+ budget: 120W total<br>
                    • 30W per port (4 ports)<br>
                    • Fanless design<br>
                    • Auto-sensing PoE
                </div>
                <div class="material-purpose">
                    Network switch providing power and data connectivity to RFID reader and other PoE devices
                </div>
            </div>

            <!-- Anker Power Supply -->
            <div class="material-card">
                <div class="material-header">
                    <div class="material-icon power">⚡</div>
                    <div class="material-info">
                        <h4>Anker Power Supply</h4>
                        <p class="material-model">Model: A2019</p>
                    </div>
                </div>
                <div class="material-qty">Quantity: 1</div>
                <div class="material-specs">
                    <strong>Specifications:</strong><br>
                    • Output: 5V/3A (15W)<br>
                    • Input: 100-240V AC<br>
                    • Efficiency: >80%<br>
                    • USB-C connector<br>
                    • Compact design
                </div>
                <div class="material-purpose">
                    External power adapter for PoE switch and system components requiring DC power
                </div>
            </div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Full Width Top Section -->
        <div class="top-section">
            <!-- Data Transmission Network -->
            <div class="card transmission-card">
                <div class="card-header">
                    <h2 class="card-title">📡 Advanced Data Transmission Network</h2>
                    <div class="transmission-controls">
                        <button class="btn btn-primary" onclick="toggleTransmissionMode()">
                            <span id="transmissionMode">🔄 Real-time Mode</span>
                        </button>
                        <button class="btn btn-success" onclick="boostSignal()">
                            ⚡ Signal Boost
                        </button>
                    </div>
                </div>
                <div class="network-diagram" id="networkDiagram">
                    <!-- Network Nodes -->
                    <div class="network-node node-cm4" data-node="cm4" title="Raspberry Pi CM4">
                        🖥️
                    </div>
                    <div class="network-node node-r420" data-node="r420" title="Impinj Speedway R420">
                        📡
                    </div>
                    <div class="network-node node-antenna node-antenna-1" data-node="antenna1" title="Laird S9028PCL #1">
                        📶
                    </div>
                    <div class="network-node node-antenna node-antenna-2" data-node="antenna2" title="Laird S9028PCL #2">
                        📶
                    </div>
                    <div class="network-node node-antenna node-antenna-3" data-node="antenna3" title="Laird S9028PCL #3">
                        📶
                    </div>
                    <div class="network-node node-antenna node-antenna-4" data-node="antenna4" title="Laird S9028PCL #4">
                        📶
                    </div>
                    <div class="network-node node-poe" data-node="poe" title="NETGEAR GS308PP PoE Switch">
                        🔌
                    </div>
                    <div class="network-node node-power" data-node="power" title="Anker 5V/3A Power Supply">
                        ⚡
                    </div>
                    <div class="network-node node-ui" data-node="ui" title="Web UI">
                        💻
                    </div>

                    <!-- Node Details Modal -->
                    <div class="node-details" id="nodeDetails">
                        <h4 id="nodeTitle"></h4>
                        <p id="nodeDescription"></p>
                        <p id="nodeSpecs"></p>
                    </div>
                </div>

                <!-- Connection Legend -->
                <div class="flex items-center gap-4 mt-2" style="font-size: 0.875rem;">
                    <div class="flex items-center gap-2">
                        <div style="width: 20px; height: 3px; background: var(--success-color); border-radius: 1px;"></div>
                        <span>RF Transmission</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div style="width: 20px; height: 4px; background: var(--primary-blue); border-radius: 2px;"></div>
                        <span>Ethernet Data</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div style="width: 20px; height: 5px; background: linear-gradient(90deg, var(--danger-color), var(--warning-color)); border-radius: 2px;"></div>
                        <span>Power Supply</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div style="width: 20px; height: 3px; background: var(--primary-green); border-radius: 1px;"></div>
                        <span>Data Flow</span>
                    </div>
                </div>
            </div>

            <!-- Warehouse Geofencing Map -->
            <div class="card warehouse-card">
                <div class="card-header">
                    <h2 class="card-title">🗺️ Advanced Warehouse Geofencing System (50m × 50m)</h2>
                    <div class="warehouse-controls">
                        <button class="btn btn-primary" onclick="toggleHeatmap()">
                            🌡️ Signal Heatmap
                        </button>
                        <button class="btn btn-warning" onclick="simulateMovement()">
                            🚚 Simulate Movement
                        </button>
                    </div>
                </div>
                <div class="warehouse-container">
                    <div class="warehouse-map" id="warehouseMap">
                        <div class="map-grid"></div>
                        <div class="map-boundary" title="45m Geofence Boundary"></div>
                        <!-- Signal strength overlay -->
                        <canvas id="signalCanvas" class="signal-overlay"></canvas>
                        <!-- Tags will be dynamically positioned here -->
                    </div>
                    <div class="warehouse-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="totalTags">30</div>
                            <div class="stat-label">Total Tags</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="activeTags">30</div>
                            <div class="stat-label">Active Tags</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="violations">0</div>
                            <div class="stat-label">Violations</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avgRSSI">-45</div>
                            <div class="stat-label">Avg RSSI (dBm)</div>
                        </div>
                    </div>
                </div>
                <div class="warehouse-legend">
                    <div class="legend-group">
                        <h4>Tag Types</h4>
                        <div class="flex items-center gap-4">
                            <div class="flex items-center gap-2">
                                <div class="legend-dot metal"></div>
                                <span>Metal Tags (Xerafy)</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="legend-dot non-metal"></div>
                                <span>Non-Metal Tags (Avery)</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="legend-dot violation"></div>
                                <span>Boundary Violations</span>
                            </div>
                        </div>
                    </div>
                    <div class="legend-group">
                        <h4>Signal Strength</h4>
                        <div class="signal-strength-bar">
                            <div class="strength-segment strong"></div>
                            <div class="strength-segment medium"></div>
                            <div class="strength-segment weak"></div>
                        </div>
                        <div class="strength-labels">
                            <span>Strong (-30 to -40 dBm)</span>
                            <span>Medium (-40 to -50 dBm)</span>
                            <span>Weak (-50 to -60 dBm)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Section with Two Columns -->
        <div class="bottom-section">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Tag Inventory -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">📋 Tag Inventory</h2>
                    <button class="btn btn-primary" onclick="scanTags()">
                        🔍 Scan Tags
                    </button>
                </div>
                <div style="overflow-x: auto;">
                    <table class="inventory-table">
                        <thead>
                            <tr>
                                <th>Tag ID</th>
                                <th>Data</th>
                                <th>RSSI</th>
                                <th>Last Seen</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Read/Write Controls -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">⚙️ Read/Write Controls</h2>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Select Tag</label>
                        <select class="form-select" id="selectedTag">
                            <option value="">Choose a tag...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Write Data</label>
                        <input type="text" class="form-input" id="writeData" placeholder="e.g., EQUIP1234">
                    </div>
                </div>
                <div class="flex gap-2 mt-2">
                    <button class="btn btn-success" onclick="writeTag()">
                        ✏️ Write Data
                    </button>
                    <button class="btn btn-primary" onclick="readTag()">
                        📖 Read Tag
                    </button>
                </div>
                <div id="operationResult" class="mt-2"></div>
            </div>
        </div>

            <!-- Right Panel -->
            <div class="right-panel">

            <!-- Settings Panel -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">⚙️ System Settings</h2>
                </div>
                <div class="settings-panel">
                    <div class="form-group">
                        <label class="form-label">RF Power Level</label>
                        <input type="range" class="power-slider" id="powerSlider" min="0" max="30" value="27" oninput="updatePower(this.value)">
                        <div class="text-center">
                            <span class="power-value" id="powerValue">27 dBm</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Antenna Read Order</label>
                        <select class="form-select" id="antennaOrder">
                            <option value="sequential">Sequential (1→2→3→4)</option>
                            <option value="parallel">Parallel (All)</option>
                            <option value="custom">Custom Order</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <button class="btn btn-warning w-full" onclick="exportCSV()">
                            📊 Export CSV Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Alerts -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">🚨 System Alerts</h2>
                </div>
                <div id="alertsContainer">
                    <!-- Dynamic alerts will be inserted here -->
                </div>
            </div>
            </div>
        </div>
    </div>

    <script>
        // System Data
        const systemData = {
            tags: [
                // Metal tags (Xerafy Dot XS)
                { id: 'XER001', type: 'metal', data: 'DRILL001', rssi: -45, x: 20, y: 30, lastSeen: new Date() },
                { id: 'XER002', type: 'metal', data: 'WELDER002', rssi: -52, x: 35, y: 25, lastSeen: new Date() },
                { id: 'XER003', type: 'metal', data: 'CRANE003', rssi: -38, x: 60, y: 40, lastSeen: new Date() },
                { id: 'XER004', type: 'metal', data: 'FORKLIFT004', rssi: -41, x: 15, y: 60, lastSeen: new Date() },
                { id: 'XER005', type: 'metal', data: 'COMPRESSOR005', rssi: -48, x: 70, y: 20, lastSeen: new Date() },
                { id: 'XER006', type: 'metal', data: 'GENERATOR006', rssi: -55, x: 25, y: 75, lastSeen: new Date() },
                { id: 'XER007', type: 'metal', data: 'PUMP007', rssi: -43, x: 80, y: 60, lastSeen: new Date() },
                { id: 'XER008', type: 'metal', data: 'MOTOR008', rssi: -50, x: 45, y: 15, lastSeen: new Date() },
                { id: 'XER009', type: 'metal', data: 'VALVE009', rssi: -46, x: 55, y: 70, lastSeen: new Date() },
                { id: 'XER010', type: 'metal', data: 'TANK010', rssi: -39, x: 30, y: 50, lastSeen: new Date() },
                { id: 'XER011', type: 'metal', data: 'CONVEYOR011', rssi: -53, x: 75, y: 35, lastSeen: new Date() },
                { id: 'XER012', type: 'metal', data: 'PRESS012', rssi: -44, x: 40, y: 80, lastSeen: new Date() },
                { id: 'XER013', type: 'metal', data: 'LATHE013', rssi: -47, x: 65, y: 25, lastSeen: new Date() },
                { id: 'XER014', type: 'metal', data: 'MILL014', rssi: -51, x: 20, y: 45, lastSeen: new Date() },
                { id: 'XER015', type: 'metal', data: 'SAW015', rssi: -42, x: 85, y: 50, lastSeen: new Date() },
                { id: 'XER016', type: 'metal', data: 'GRINDER016', rssi: -49, x: 50, y: 35, lastSeen: new Date() },
                { id: 'XER017', type: 'metal', data: 'ROBOT017', rssi: -40, x: 30, y: 65, lastSeen: new Date() },
                { id: 'XER018', type: 'metal', data: 'HOIST018', rssi: -54, x: 70, y: 75, lastSeen: new Date() },
                { id: 'XER019', type: 'metal', data: 'JACK019', rssi: -45, x: 15, y: 35, lastSeen: new Date() },
                { id: 'XER020', type: 'metal', data: 'WINCH020', rssi: -48, x: 60, y: 55, lastSeen: new Date() },

                // Non-metal tags (Avery Dennison AD-160u7)
                { id: 'AVE001', type: 'non-metal', data: 'PALLET001', rssi: -35, x: 25, y: 20, lastSeen: new Date() },
                { id: 'AVE002', type: 'non-metal', data: 'CONTAINER002', rssi: -42, x: 45, y: 60, lastSeen: new Date() },
                { id: 'AVE003', type: 'non-metal', data: 'BOX003', rssi: -38, x: 65, y: 30, lastSeen: new Date() },
                { id: 'AVE004', type: 'non-metal', data: 'CRATE004', rssi: -44, x: 35, y: 70, lastSeen: new Date() },
                { id: 'AVE005', type: 'non-metal', data: 'BARREL005', rssi: -40, x: 55, y: 45, lastSeen: new Date() },
                { id: 'AVE006', type: 'non-metal', data: 'RACK006', rssi: -46, x: 75, y: 65, lastSeen: new Date() },
                { id: 'AVE007', type: 'non-metal', data: 'SHELF007', rssi: -36, x: 20, y: 55, lastSeen: new Date() },
                { id: 'AVE008', type: 'non-metal', data: 'CART008', rssi: -43, x: 80, y: 40, lastSeen: new Date() },
                { id: 'AVE009', type: 'non-metal', data: 'TROLLEY009', rssi: -41, x: 40, y: 25, lastSeen: new Date() },
                { id: 'AVE010', type: 'non-metal', data: 'PLATFORM010', rssi: -39, x: 50, y: 75, lastSeen: new Date() }
            ],
            nodeDetails: {
                cm4: {
                    title: 'Raspberry Pi CM4',
                    description: 'Central processing unit with 4GB RAM, 32GB eMMC',
                    specs: 'ARM Cortex-A72 quad-core, WiFi, Bluetooth, GPIO'
                },
                r420: {
                    title: 'Impinj Speedway R420',
                    description: 'UHF RFID reader with 4 antenna ports',
                    specs: '902-928 MHz, 50m range, PoE powered, 30 dBm max power'
                },
                antenna1: {
                    title: 'Laird S9028PCL Antenna #1',
                    description: 'Circular polarized antenna (8 dBi gain)',
                    specs: '902-928 MHz, IP67 rated, 50m effective range'
                },
                antenna2: {
                    title: 'Laird S9028PCL Antenna #2',
                    description: 'Circular polarized antenna (8 dBi gain)',
                    specs: '902-928 MHz, IP67 rated, 50m effective range'
                },
                antenna3: {
                    title: 'Laird S9028PCL Antenna #3',
                    description: 'Circular polarized antenna (8 dBi gain)',
                    specs: '902-928 MHz, IP67 rated, 50m effective range'
                },
                antenna4: {
                    title: 'Laird S9028PCL Antenna #4',
                    description: 'Circular polarized antenna (8 dBi gain)',
                    specs: '902-928 MHz, IP67 rated, 50m effective range'
                },
                poe: {
                    title: 'NETGEAR GS308PP PoE Switch',
                    description: '8-port Gigabit PoE+ switch',
                    specs: '120W PoE budget, fanless, auto-sensing'
                },
                power: {
                    title: 'Anker 5V/3A Power Supply',
                    description: 'External power adapter for PoE switch',
                    specs: '15W output, universal input, compact design'
                },
                ui: {
                    title: 'Web User Interface',
                    description: 'Browser-based control interface',
                    specs: 'Real-time monitoring, responsive design, mobile compatible'
                }
            }
        };

        let currentPower = 27;
        let isScanning = false;
        let dataFlowInterval;
        let transmissionMode = 'realtime';
        let heatmapEnabled = false;
        let signalCanvas;
        let signalCtx;

        // Initialize the simulation
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth transitions first
            addSmoothTransitions();

            initializeSimulation();
            initializeSignalCanvas();
            startDataFlowAnimation();
            updateInventoryTable();
            renderWarehouseMap();
            updateWarehouseStats();
            updateUptime();

            // Update uptime every minute
            setInterval(updateUptime, 60000);

            // Simulate tag movement every 10 seconds
            setInterval(simulateTagMovement, 10000);

            // Add periodic signal waves for visual appeal
            setInterval(() => {
                if (!isScanning && Math.random() > 0.7) {
                    createSignalWaves();
                }
            }, 5000);
        });

        // Initialize simulation
        function initializeSimulation() {
            // Add click handlers for network nodes
            document.querySelectorAll('.network-node').forEach(node => {
                node.addEventListener('click', showNodeDetails);
            });

            // Add enhanced interactions for material cards
            document.querySelectorAll('.material-card').forEach(card => {
                card.addEventListener('click', function() {
                    // Add a subtle animation when clicked
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                    this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.2)';

                    setTimeout(() => {
                        this.style.transform = 'translateY(-8px) scale(1.02)';
                        this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
                    }, 200);
                });

                // Add hover interconnection effects
                card.addEventListener('mouseenter', function() {
                    const cardType = this.querySelector('.material-icon').classList[1];
                    highlightRelatedComponents(cardType);
                });

                card.addEventListener('mouseleave', function() {
                    clearComponentHighlights();
                });
            });

            // Populate tag dropdown
            const tagSelect = document.getElementById('selectedTag');
            systemData.tags.forEach(tag => {
                const option = document.createElement('option');
                option.value = tag.id;
                option.textContent = `${tag.id} (${tag.data})`;
                tagSelect.appendChild(option);
            });

            // Initialize alerts
            checkGeofenceViolations();

            // Add a delay before creating connections to ensure DOM is ready
            setTimeout(() => {
                createStaticConnections();
            }, 500);
        }

        // Show node details on click
        function showNodeDetails(event) {
            const nodeType = event.target.dataset.node;
            const details = systemData.nodeDetails[nodeType];

            if (details) {
                const detailsEl = document.getElementById('nodeDetails');
                document.getElementById('nodeTitle').textContent = details.title;
                document.getElementById('nodeDescription').textContent = details.description;
                document.getElementById('nodeSpecs').textContent = details.specs;

                detailsEl.style.left = event.pageX + 10 + 'px';
                detailsEl.style.top = event.pageY + 10 + 'px';
                detailsEl.classList.add('show');

                // Hide after 3 seconds
                setTimeout(() => {
                    detailsEl.classList.remove('show');
                }, 3000);
            }
        }

        // Start data flow animation
        function startDataFlowAnimation() {
            // Start animated data flows (static connections created in initializeSimulation)
            dataFlowInterval = setInterval(() => {
                createDataFlowLine();
            }, 1500);
        }

        // Create static connection lines between components
        function createStaticConnections() {
            const diagram = document.getElementById('networkDiagram');
            const connections = [
                // Power connections (red)
                { from: 'power', to: 'poe', type: 'power' },
                { from: 'poe', to: 'r420', type: 'power' },

                // Ethernet connections (blue)
                { from: 'r420', to: 'cm4', type: 'ethernet' },
                { from: 'cm4', to: 'ui', type: 'ethernet' },

                // RF connections (green - from antennas to R420)
                { from: 'antenna1', to: 'r420', type: 'rf' },
                { from: 'antenna2', to: 'r420', type: 'rf' },
                { from: 'antenna3', to: 'r420', type: 'rf' },
                { from: 'antenna4', to: 'r420', type: 'rf' }
            ];

            connections.forEach(conn => {
                const fromNode = document.querySelector(`[data-node="${conn.from}"]`);
                const toNode = document.querySelector(`[data-node="${conn.to}"]`);

                if (fromNode && toNode) {
                    const line = createConnectionLine(fromNode, toNode, conn.type);
                    diagram.appendChild(line);
                }
            });
        }

        // Create a connection line between two nodes
        function createConnectionLine(fromNode, toNode, type) {
            const fromRect = fromNode.getBoundingClientRect();
            const toRect = toNode.getBoundingClientRect();
            const diagramRect = document.getElementById('networkDiagram').getBoundingClientRect();

            const fromX = fromRect.left + fromRect.width / 2 - diagramRect.left;
            const fromY = fromRect.top + fromRect.height / 2 - diagramRect.top;
            const toX = toRect.left + toRect.width / 2 - diagramRect.left;
            const toY = toRect.top + toRect.height / 2 - diagramRect.top;

            const length = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2));
            const angle = Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI;

            const line = document.createElement('div');
            line.className = `connection-line ${type}`;
            line.style.left = fromX + 'px';
            line.style.top = fromY + 'px';
            line.style.width = length + 'px';
            line.style.transform = `rotate(${angle}deg)`;
            line.style.transformOrigin = '0 50%';

            return line;
        }

        // Create animated data flow lines with particles
        function createDataFlowLine() {
            const diagram = document.getElementById('networkDiagram');

            // Define specific data flow paths
            const dataFlows = [
                // Tag reads: Antennas → R420 → CM4 → UI
                { from: 'antenna1', to: 'r420', type: 'data', speed: 'normal' },
                { from: 'antenna2', to: 'r420', type: 'data', speed: 'normal' },
                { from: 'antenna3', to: 'r420', type: 'data', speed: 'normal' },
                { from: 'antenna4', to: 'r420', type: 'data', speed: 'normal' },
                { from: 'r420', to: 'cm4', type: 'data', speed: 'fast' },
                { from: 'cm4', to: 'ui', type: 'data', speed: 'fast' }
            ];

            // Pick a random data flow
            const flow = dataFlows[Math.floor(Math.random() * dataFlows.length)];
            const fromNode = document.querySelector(`[data-node="${flow.from}"]`);
            const toNode = document.querySelector(`[data-node="${flow.to}"]`);

            if (fromNode && toNode) {
                // Add transmitting effect to source node
                fromNode.classList.add('transmitting');
                setTimeout(() => fromNode.classList.remove('transmitting'), 1500);

                // Create data particle
                createDataParticle(fromNode, toNode, flow.speed);

                // Add active effect to destination node
                setTimeout(() => {
                    toNode.classList.add('active');
                    setTimeout(() => toNode.classList.remove('active'), 500);
                }, flow.speed === 'fast' ? 500 : 1000);
            }
        }

        // Create animated data particles
        function createDataParticle(fromNode, toNode, speed) {
            const diagram = document.getElementById('networkDiagram');
            const fromRect = fromNode.getBoundingClientRect();
            const toRect = toNode.getBoundingClientRect();
            const diagramRect = diagram.getBoundingClientRect();

            const fromX = fromRect.left + fromRect.width / 2 - diagramRect.left;
            const fromY = fromRect.top + fromRect.height / 2 - diagramRect.top;
            const toX = toRect.left + toRect.width / 2 - diagramRect.left;
            const toY = toRect.top + toRect.height / 2 - diagramRect.top;

            const particle = document.createElement('div');
            particle.className = `data-particle ${speed === 'fast' ? 'fast' : ''}`;
            particle.style.left = fromX + 'px';
            particle.style.top = fromY + 'px';

            // Calculate movement
            const deltaX = toX - fromX;
            const deltaY = toY - fromY;

            particle.style.setProperty('--deltaX', deltaX + 'px');
            particle.style.setProperty('--deltaY', deltaY + 'px');

            // Custom animation for particle movement
            particle.style.animation = `particleMove${speed === 'fast' ? 'Fast' : ''} ${speed === 'fast' ? '1s' : '2s'} linear`;

            diagram.appendChild(particle);

            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, speed === 'fast' ? 1000 : 2000);
        }

        // Create signal waves from antennas during scanning
        function createSignalWaves() {
            const antennas = document.querySelectorAll('.node-antenna');
            antennas.forEach(antenna => {
                const wave = document.createElement('div');
                wave.className = 'signal-wave';

                const rect = antenna.getBoundingClientRect();
                const diagramRect = document.getElementById('networkDiagram').getBoundingClientRect();

                wave.style.left = (rect.left + rect.width / 2 - diagramRect.left - 20) + 'px';
                wave.style.top = (rect.top + rect.height / 2 - diagramRect.top - 20) + 'px';
                wave.style.width = '40px';
                wave.style.height = '40px';

                document.getElementById('networkDiagram').appendChild(wave);

                setTimeout(() => {
                    if (wave.parentNode) {
                        wave.parentNode.removeChild(wave);
                    }
                }, 3000);
            });
        }

        // Update inventory table
        function updateInventoryTable() {
            const tbody = document.getElementById('inventoryTableBody');
            tbody.innerHTML = '';

            systemData.tags.forEach(tag => {
                const row = document.createElement('tr');

                // Calculate RSSI strength
                let rssiClass = 'rssi-weak';
                let rssiWidth = '20%';
                if (tag.rssi > -40) {
                    rssiClass = 'rssi-strong';
                    rssiWidth = '80%';
                } else if (tag.rssi > -50) {
                    rssiClass = 'rssi-medium';
                    rssiWidth = '60%';
                }

                // Calculate location based on coordinates
                const location = `Zone ${Math.ceil(tag.x / 25)}-${Math.ceil(tag.y / 25)}`;

                row.innerHTML = `
                    <td>${tag.id}</td>
                    <td>${tag.data}</td>
                    <td>
                        <div class="rssi-indicator">
                            <div class="rssi-bar ${rssiClass}" style="width: ${rssiWidth}"></div>
                        </div>
                        ${tag.rssi} dBm
                    </td>
                    <td>${tag.lastSeen.toLocaleTimeString()}</td>
                    <td>${location}</td>
                `;

                tbody.appendChild(row);
            });
        }

        // Render warehouse map with tags
        function renderWarehouseMap() {
            const map = document.getElementById('warehouseMap');

            // Clear existing tags
            const existingTags = map.querySelectorAll('.map-tag');
            existingTags.forEach(tag => tag.remove());

            systemData.tags.forEach(tag => {
                const tagEl = document.createElement('div');
                tagEl.className = `map-tag ${tag.type}`;
                tagEl.style.left = tag.x + '%';
                tagEl.style.top = tag.y + '%';
                tagEl.title = `${tag.id}: ${tag.data} (${tag.rssi} dBm)`;

                // Check for boundary violations (45m = 90% of 50m)
                if (tag.x > 90 || tag.y > 90 || tag.x < 10 || tag.y < 10) {
                    tagEl.classList.add('violation');
                }

                map.appendChild(tagEl);
            });
        }

        // Check for geofence violations
        function checkGeofenceViolations() {
            const violations = systemData.tags.filter(tag =>
                tag.x > 90 || tag.y > 90 || tag.x < 10 || tag.y < 10
            );

            const alertsContainer = document.getElementById('alertsContainer');
            alertsContainer.innerHTML = '';

            if (violations.length > 0) {
                violations.forEach(tag => {
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-danger';
                    alert.innerHTML = `
                        <strong>Geofence Violation!</strong><br>
                        Tag ${tag.id} (${tag.data}) is outside the 45m boundary.
                        <br><small>Current RSSI: ${tag.rssi} dBm</small>
                    `;
                    alertsContainer.appendChild(alert);
                });
            } else {
                const alert = document.createElement('div');
                alert.className = 'alert alert-success';
                alert.innerHTML = '<strong>All Clear!</strong><br>All tags are within the geofence boundary.';
                alertsContainer.appendChild(alert);
            }
        }

        // Enhanced scan tags function with visual effects
        function scanTags() {
            isScanning = true;
            const btn = event.target;
            btn.innerHTML = '<span class="loading-spinner"></span>Scanning...';
            btn.disabled = true;

            // Add scanning indicators to antennas
            const antennas = document.querySelectorAll('.node-antenna');
            antennas.forEach(antenna => {
                const indicator = document.createElement('div');
                indicator.className = 'scanning-indicator';
                antenna.appendChild(indicator);
            });

            // Create signal waves
            createSignalWaves();

            // Simulate progressive scanning with visual feedback
            let scannedCount = 0;
            const scanInterval = setInterval(() => {
                if (scannedCount < systemData.tags.length) {
                    // Highlight tags being scanned on the map
                    const tagElements = document.querySelectorAll('.map-tag');
                    if (tagElements[scannedCount]) {
                        tagElements[scannedCount].style.animation = 'scanPulse 0.5s ease-in-out';
                        setTimeout(() => {
                            if (tagElements[scannedCount]) {
                                tagElements[scannedCount].style.animation = '';
                            }
                        }, 500);
                    }

                    scannedCount++;
                    btn.innerHTML = `<span class="loading-spinner"></span>Scanning... (${scannedCount}/${systemData.tags.length})`;
                } else {
                    clearInterval(scanInterval);

                    // Update tag data
                    systemData.tags.forEach(tag => {
                        tag.lastSeen = new Date();
                        // Simulate RSSI variations
                        tag.rssi += Math.random() * 6 - 3; // ±3 dBm variation
                        tag.rssi = Math.max(-60, Math.min(-30, tag.rssi)); // Keep in realistic range
                    });

                    // Remove scanning indicators
                    document.querySelectorAll('.scanning-indicator').forEach(indicator => {
                        indicator.remove();
                    });

                    updateInventoryTable();
                    renderWarehouseMap();
                    checkGeofenceViolations();

                    btn.innerHTML = '🔍 Scan Tags';
                    btn.disabled = false;
                    isScanning = false;

                    // Show success message with enhanced feedback
                    showAlert('success', `✅ Successfully scanned ${systemData.tags.length} tags in ${(scannedCount * 100)}ms`);

                    // Create celebration effect
                    createSignalWaves();
                }
            }, 100);
        }

        // Read tag function
        function readTag() {
            const selectedTagId = document.getElementById('selectedTag').value;
            if (!selectedTagId) {
                showAlert('warning', 'Please select a tag to read');
                return;
            }

            const tag = systemData.tags.find(t => t.id === selectedTagId);
            if (tag) {
                const result = document.getElementById('operationResult');
                result.innerHTML = `
                    <div class="alert alert-success">
                        <strong>Read Successful!</strong><br>
                        Tag ID: ${tag.id}<br>
                        Data: ${tag.data}<br>
                        RSSI: ${tag.rssi} dBm<br>
                        Type: ${tag.type === 'metal' ? 'Xerafy Dot XS (Metal)' : 'Avery Dennison AD-160u7 (Non-Metal)'}
                    </div>
                `;
            }
        }

        // Write tag function
        function writeTag() {
            const selectedTagId = document.getElementById('selectedTag').value;
            const writeData = document.getElementById('writeData').value;

            if (!selectedTagId) {
                showAlert('warning', 'Please select a tag to write to');
                return;
            }

            if (!writeData) {
                showAlert('warning', 'Please enter data to write');
                return;
            }

            const tag = systemData.tags.find(t => t.id === selectedTagId);
            if (tag) {
                // Simulate write operation
                tag.data = writeData;
                tag.lastSeen = new Date();

                updateInventoryTable();

                const result = document.getElementById('operationResult');
                result.innerHTML = `
                    <div class="alert alert-success">
                        <strong>Write Successful!</strong><br>
                        Tag ID: ${tag.id}<br>
                        New Data: ${writeData}<br>
                        Write Range: ${tag.type === 'metal' ? '20-30m' : '25-35m'}
                    </div>
                `;

                // Clear the input
                document.getElementById('writeData').value = '';
            }
        }

        // Update power setting
        function updatePower(value) {
            currentPower = value;
            document.getElementById('powerValue').textContent = value + ' dBm';

            // Simulate power effect on range
            const rangeEffect = value / 30; // 0 to 1
            systemData.tags.forEach(tag => {
                // Adjust RSSI based on power setting
                const baseRSSI = tag.rssi;
                const powerAdjustment = (value - 27) * 2; // ±6 dBm range
                tag.rssi = Math.max(-60, Math.min(-30, baseRSSI + powerAdjustment));
            });

            updateInventoryTable();
            renderWarehouseMap();
        }

        // Export CSV function
        function exportCSV() {
            const headers = ['Tag ID', 'Data', 'RSSI (dBm)', 'Last Seen', 'Location', 'Type'];
            const csvContent = [
                headers.join(','),
                ...systemData.tags.map(tag => [
                    tag.id,
                    tag.data,
                    tag.rssi,
                    tag.lastSeen.toISOString(),
                    `Zone ${Math.ceil(tag.x / 25)}-${Math.ceil(tag.y / 25)}`,
                    tag.type === 'metal' ? 'Xerafy Dot XS' : 'Avery Dennison AD-160u7'
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rfid_inventory_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);

            showAlert('success', 'CSV report exported successfully');
        }

        // Update uptime display
        function updateUptime() {
            const startTime = new Date('2024-01-01T00:00:00');
            const now = new Date();
            const diff = now - startTime;

            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

            document.getElementById('uptime').textContent = `CM4 Uptime: ${hours}h ${minutes}m`;
        }

        // Show alert function
        function showAlert(type, message) {
            const alertsContainer = document.getElementById('alertsContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `<strong>${type.charAt(0).toUpperCase() + type.slice(1)}!</strong><br>${message}`;

            // Insert at the top
            alertsContainer.insertBefore(alert, alertsContainer.firstChild);

            // Remove after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);

            // Keep only last 3 alerts
            while (alertsContainer.children.length > 3) {
                alertsContainer.removeChild(alertsContainer.lastChild);
            }
        }

        // Highlight related components when hovering over material cards
        function highlightRelatedComponents(cardType) {
            const networkNodes = document.querySelectorAll('.network-node');

            switch(cardType) {
                case 'processing':
                    highlightNode('cm4');
                    break;
                case 'reader':
                    highlightNode('r420');
                    break;
                case 'antenna':
                    highlightNode('antenna1');
                    highlightNode('antenna2');
                    highlightNode('antenna3');
                    highlightNode('antenna4');
                    break;
                case 'network':
                    highlightNode('poe');
                    break;
                case 'power':
                    highlightNode('power');
                    break;
                case 'tag-metal':
                case 'tag-nonmetal':
                    // Highlight tags on the map
                    const mapTags = document.querySelectorAll(`.map-tag.${cardType === 'tag-metal' ? 'metal' : 'non-metal'}`);
                    mapTags.forEach(tag => {
                        tag.style.animation = 'pulse 1s infinite';
                    });
                    break;
            }
        }

        function highlightNode(nodeId) {
            const node = document.querySelector(`[data-node="${nodeId}"]`);
            if (node) {
                node.classList.add('active');
            }
        }

        function clearComponentHighlights() {
            // Clear network node highlights
            document.querySelectorAll('.network-node').forEach(node => {
                node.classList.remove('active');
            });

            // Clear map tag highlights
            document.querySelectorAll('.map-tag').forEach(tag => {
                tag.style.animation = '';
            });
        }

        // Enhanced tag movement with smooth transitions
        function simulateTagMovement() {
            if (!isScanning) {
                // Move a random tag slightly with smooth animation
                const randomTag = systemData.tags[Math.floor(Math.random() * systemData.tags.length)];
                const oldX = randomTag.x;
                const oldY = randomTag.y;

                randomTag.x += (Math.random() - 0.5) * 5; // ±2.5% movement
                randomTag.y += (Math.random() - 0.5) * 5;

                // Keep within bounds
                randomTag.x = Math.max(5, Math.min(95, randomTag.x));
                randomTag.y = Math.max(5, Math.min(95, randomTag.y));

                // Update displays with smooth transition
                renderWarehouseMap();
                checkGeofenceViolations();

                // Create a brief data transmission effect for the moved tag
                if (Math.abs(randomTag.x - oldX) > 2 || Math.abs(randomTag.y - oldY) > 2) {
                    // Simulate tag reporting its new position
                    setTimeout(() => {
                        createDataFlowLine();
                    }, Math.random() * 1000);
                }
            }
        }

        // Add smooth transitions to all interactive elements
        function addSmoothTransitions() {
            const style = document.createElement('style');
            style.textContent = `
                .network-node, .map-tag, .material-card {
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .btn {
                    transition: all 0.2s ease-in-out;
                }

                .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                .inventory-table tr {
                    transition: background-color 0.2s ease;
                }

                .inventory-table tr:hover {
                    background-color: var(--bg-gray-50);
                }
            `;
            document.head.appendChild(style);
        }

        // Initialize signal canvas for heatmap
        function initializeSignalCanvas() {
            signalCanvas = document.getElementById('signalCanvas');
            signalCtx = signalCanvas.getContext('2d');

            // Set canvas size to match warehouse map
            const warehouseMap = document.getElementById('warehouseMap');
            signalCanvas.width = warehouseMap.offsetWidth;
            signalCanvas.height = warehouseMap.offsetHeight;

            // Resize canvas when window resizes
            window.addEventListener('resize', () => {
                signalCanvas.width = warehouseMap.offsetWidth;
                signalCanvas.height = warehouseMap.offsetHeight;
                if (heatmapEnabled) {
                    drawSignalHeatmap();
                }
            });
        }

        // Toggle transmission mode
        function toggleTransmissionMode() {
            transmissionMode = transmissionMode === 'realtime' ? 'burst' : 'realtime';
            const modeElement = document.getElementById('transmissionMode');

            if (transmissionMode === 'burst') {
                modeElement.textContent = '💥 Burst Mode';
                // Increase data flow frequency
                clearInterval(dataFlowInterval);
                dataFlowInterval = setInterval(() => {
                    createDataFlowLine();
                    // Create multiple particles in burst mode
                    setTimeout(() => createDataFlowLine(), 200);
                    setTimeout(() => createDataFlowLine(), 400);
                }, 800);
            } else {
                modeElement.textContent = '🔄 Real-time Mode';
                // Normal data flow frequency
                clearInterval(dataFlowInterval);
                dataFlowInterval = setInterval(() => {
                    createDataFlowLine();
                }, 1500);
            }

            showAlert('success', `Transmission mode changed to ${transmissionMode}`);
        }

        // Signal boost effect
        function boostSignal() {
            // Add boost effect to all antennas
            const antennas = document.querySelectorAll('.node-antenna');
            antennas.forEach(antenna => {
                antenna.style.animation = 'nodePulse 0.5s ease-in-out 3';
                antenna.style.boxShadow = '0 0 30px rgba(16, 185, 129, 1)';
            });

            // Create multiple signal waves
            for (let i = 0; i < 5; i++) {
                setTimeout(() => createSignalWaves(), i * 200);
            }

            // Boost RSSI temporarily
            systemData.tags.forEach(tag => {
                tag.rssi += 5; // Boost signal
                tag.rssi = Math.min(-30, tag.rssi); // Cap at maximum
            });

            // Reset after 3 seconds
            setTimeout(() => {
                antennas.forEach(antenna => {
                    antenna.style.animation = '';
                    antenna.style.boxShadow = '';
                });

                systemData.tags.forEach(tag => {
                    tag.rssi -= 5; // Return to normal
                    tag.rssi = Math.max(-60, tag.rssi); // Cap at minimum
                });

                updateInventoryTable();
                updateWarehouseStats();
            }, 3000);

            updateInventoryTable();
            updateWarehouseStats();
            showAlert('success', '⚡ Signal boosted for 3 seconds!');
        }

        // Toggle signal heatmap
        function toggleHeatmap() {
            heatmapEnabled = !heatmapEnabled;
            const btn = event.target;

            if (heatmapEnabled) {
                btn.textContent = '🌡️ Hide Heatmap';
                drawSignalHeatmap();
            } else {
                btn.textContent = '🌡️ Signal Heatmap';
                signalCtx.clearRect(0, 0, signalCanvas.width, signalCanvas.height);
            }
        }

        // Draw signal strength heatmap
        function drawSignalHeatmap() {
            if (!signalCtx) return;

            signalCtx.clearRect(0, 0, signalCanvas.width, signalCanvas.height);

            const width = signalCanvas.width;
            const height = signalCanvas.height;
            const imageData = signalCtx.createImageData(width, height);
            const data = imageData.data;

            // Calculate signal strength for each pixel
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const pixelIndex = (y * width + x) * 4;

                    // Calculate distance to nearest antenna (simplified)
                    const centerX = width / 2;
                    const centerY = height / 2;
                    const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
                    const maxDistance = Math.sqrt(Math.pow(width / 2, 2) + Math.pow(height / 2, 2));

                    // Signal strength decreases with distance
                    const strength = Math.max(0, 1 - (distance / maxDistance));

                    // Color based on signal strength
                    if (strength > 0.7) {
                        // Strong signal - green
                        data[pixelIndex] = 16;     // R
                        data[pixelIndex + 1] = 185; // G
                        data[pixelIndex + 2] = 129; // B
                        data[pixelIndex + 3] = strength * 100; // A
                    } else if (strength > 0.4) {
                        // Medium signal - yellow
                        data[pixelIndex] = 245;     // R
                        data[pixelIndex + 1] = 158; // G
                        data[pixelIndex + 2] = 11;  // B
                        data[pixelIndex + 3] = strength * 80; // A
                    } else {
                        // Weak signal - red
                        data[pixelIndex] = 239;     // R
                        data[pixelIndex + 1] = 68;  // G
                        data[pixelIndex + 2] = 68;  // B
                        data[pixelIndex + 3] = strength * 60; // A
                    }
                }
            }

            signalCtx.putImageData(imageData, 0, 0);
        }

        // Simulate equipment movement
        function simulateMovement() {
            const btn = event.target;
            btn.innerHTML = '<span class="loading-spinner"></span>Simulating...';
            btn.disabled = true;

            // Move multiple tags with realistic patterns
            const movingTags = systemData.tags.slice(0, 8); // Move 8 tags
            let moveCount = 0;

            const moveInterval = setInterval(() => {
                if (moveCount < 20) { // 20 movement steps
                    movingTags.forEach(tag => {
                        // Simulate realistic movement patterns
                        const movement = 3; // 3% movement per step
                        tag.x += (Math.random() - 0.5) * movement;
                        tag.y += (Math.random() - 0.5) * movement;

                        // Keep within bounds
                        tag.x = Math.max(5, Math.min(95, tag.x));
                        tag.y = Math.max(5, Math.min(95, tag.y));

                        // Update last seen time
                        tag.lastSeen = new Date();
                    });

                    renderWarehouseMap();
                    updateInventoryTable();
                    checkGeofenceViolations();
                    updateWarehouseStats();

                    // Create data transmission for moving tags
                    if (moveCount % 3 === 0) {
                        createDataFlowLine();
                    }

                    moveCount++;
                } else {
                    clearInterval(moveInterval);
                    btn.innerHTML = '🚚 Simulate Movement';
                    btn.disabled = false;
                    showAlert('success', '✅ Movement simulation completed');
                }
            }, 300);
        }

        // Update warehouse statistics
        function updateWarehouseStats() {
            const totalTags = systemData.tags.length;
            const activeTags = systemData.tags.filter(tag =>
                (new Date() - tag.lastSeen) < 30000 // Active in last 30 seconds
            ).length;
            const violations = systemData.tags.filter(tag =>
                tag.x > 90 || tag.y > 90 || tag.x < 10 || tag.y < 10
            ).length;
            const avgRSSI = Math.round(
                systemData.tags.reduce((sum, tag) => sum + tag.rssi, 0) / totalTags
            );

            document.getElementById('totalTags').textContent = totalTags;
            document.getElementById('activeTags').textContent = activeTags;
            document.getElementById('violations').textContent = violations;
            document.getElementById('avgRSSI').textContent = avgRSSI;

            // Update violation indicator color
            const violationElement = document.getElementById('violations');
            if (violations > 0) {
                violationElement.style.color = '#ef4444';
                violationElement.style.animation = 'pulse 1s infinite';
            } else {
                violationElement.style.color = '#60a5fa';
                violationElement.style.animation = '';
            }
        }
    </script>
</body>
</html>
