<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Warehouse RFID System Architecture</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dark-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --border-radius: 20px;
            --border-radius-sm: 12px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            line-height: 1.6;
            color: var(--text-primary);
            overflow-x: hidden;
        }

        .main-container {
            max-width: 100vw;
            margin: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            min-height: 100vh;
        }

        .header {
            background: var(--dark-gradient);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: clamp(24px, 5vw, 48px);
            font-weight: 700;
            letter-spacing: -0.02em;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: clamp(14px, 2.5vw, 20px);
            opacity: 0.9;
            font-weight: 400;
            max-width: 800px;
            margin: 0 auto;
        }

        .header-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: clamp(20px, 3vw, 32px);
            font-weight: 700;
            color: #38ef7d;
        }

        .stat-label {
            font-size: clamp(10px, 1.5vw, 14px);
            opacity: 0.8;
            margin-top: 4px;
        }

        .content-wrapper {
            padding: 20px;
        }

        /* Responsive breakpoints */
        @media (min-width: 768px) {
            body {
                padding: 20px;
            }
            .header {
                padding: 30px;
            }
            .content-wrapper {
                padding: 30px;
            }
        }

        @media (min-width: 1200px) {
            .main-container {
                max-width: 1600px;
            }
            .header {
                padding: 40px;
            }
            .content-wrapper {
                padding: 40px;
            }
        }
        .materials-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: var(--shadow-xl);
        }

        .section-title {
            font-size: clamp(24px, 4vw, 36px);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 32px;
            position: relative;
            text-align: center;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--info-gradient);
            border-radius: 2px;
        }

        .materials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .material-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-sm);
            padding: 24px;
            box-shadow: var(--shadow-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 320px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .material-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--info-gradient);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .material-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25);
        }

        .material-card:hover::before {
            transform: scaleX(1);
        }

        /* Responsive adjustments for materials */
        @media (min-width: 768px) {
            .materials-section {
                padding: 25px;
                margin-bottom: 25px;
            }
            .materials-grid {
                gap: 20px;
                margin-bottom: 25px;
            }
            .material-card {
                padding: 18px;
            }
        }

        @media (min-width: 1200px) {
            .materials-section {
                padding: 30px;
                margin-bottom: 30px;
            }
            .materials-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
                margin-bottom: 30px;
            }
            .material-card {
                padding: 20px;
            }
        }
        .material-image {
            width: 100%;
            height: 140px;
            object-fit: cover;
            border-radius: var(--border-radius-sm);
            margin-bottom: 20px;
            flex-shrink: 0;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .material-card:hover .material-image {
            transform: scale(1.05);
        }

        .material-title {
            font-size: clamp(16px, 2.5vw, 20px);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .material-model {
            font-size: clamp(12px, 2vw, 14px);
            color: var(--text-secondary);
            margin-bottom: 12px;
            font-weight: 500;
        }

        .material-qty {
            background: var(--warning-gradient);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: clamp(11px, 2vw, 13px);
            font-weight: 600;
            display: inline-block;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
        }

        .material-purpose {
            font-size: clamp(13px, 2.2vw, 15px);
            color: var(--text-primary);
            line-height: 1.5;
            margin-bottom: 16px;
            flex-grow: 1;
        }

        .material-specs {
            font-size: clamp(11px, 1.8vw, 13px);
            color: var(--text-secondary);
            line-height: 1.4;
            margin-top: auto;
            padding-top: 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Responsive adjustments for material content */
        @media (min-width: 768px) {
            .material-image {
                height: 110px;
                margin-bottom: 14px;
            }
            .material-title {
                margin-bottom: 7px;
            }
            .material-model {
                margin-bottom: 9px;
            }
            .material-qty {
                padding: 4px 11px;
                margin-bottom: 9px;
            }
            .material-purpose {
                margin-bottom: 9px;
            }
        }

        @media (min-width: 1200px) {
            .material-image {
                height: 120px;
                margin-bottom: 15px;
            }
            .material-title {
                margin-bottom: 8px;
            }
            .material-model {
                margin-bottom: 10px;
            }
            .material-qty {
                padding: 4px 12px;
                margin-bottom: 10px;
            }
            .material-purpose {
                margin-bottom: 10px;
            }
        }
        .diagram-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-xl);
            margin-bottom: 40px;
        }

        .architecture-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
            margin-top: 32px;
        }

        .architecture-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-sm);
            padding: 32px;
            box-shadow: var(--shadow-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .architecture-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-gradient);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .architecture-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25);
        }

        .architecture-card:hover::before {
            transform: scaleX(1);
        }

        /* Responsive adjustments for diagram section */
        @media (min-width: 768px) {
            .diagram-section {
                padding: 25px;
            }
            .legend {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 12px;
                margin-bottom: 22px;
                padding: 18px;
            }
            .legend-line {
                width: 28px;
                height: 3.5px;
                margin-right: 9px;
            }
        }

        @media (min-width: 1200px) {
            .diagram-section {
                padding: 30px;
            }
            .legend {
                gap: 15px;
                margin-bottom: 25px;
                padding: 20px;
            }
            .legend-line {
                width: 30px;
                height: 4px;
                margin-right: 10px;
            }
        }
        .power-line { background: linear-gradient(90deg, #e74c3c, #c0392b); }
        .data-line { background: linear-gradient(90deg, #3498db, #2980b9); }
        .rf-line {
            background: linear-gradient(90deg, #e67e22, #d35400);
            background-image: repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(255,255,255,0.3) 3px, rgba(255,255,255,0.3) 6px);
        }
        .cellular-line {
            background: linear-gradient(90deg, #27ae60, #229954);
            background-image: repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(255,255,255,0.3) 3px, rgba(255,255,255,0.3) 6px);
        }

        /* Responsive Architecture Section */
        .architecture-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .architecture-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .system-component {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .system-component:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .component-power { border-left-color: #e74c3c; }
        .component-network { border-left-color: #3498db; }
        .component-processing { border-left-color: #9b59b6; }
        .component-rfid { border-left-color: #e67e22; }
        .component-cellular { border-left-color: #27ae60; }

        .component-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .component-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }

        .icon-power { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .icon-network { background: linear-gradient(135deg, #3498db, #2980b9); }
        .icon-processing { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .icon-rfid { background: linear-gradient(135deg, #e67e22, #d35400); }
        .icon-cellular { background: linear-gradient(135deg, #27ae60, #229954); }

        .component-title {
            font-size: clamp(14px, 2.5vw, 18px);
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }

        .component-subtitle {
            font-size: clamp(11px, 2vw, 13px);
            color: #7f8c8d;
            margin: 5px 0 0 0;
        }

        .component-specs {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .component-specs li {
            font-size: clamp(10px, 1.8vw, 12px);
            color: #2c3e50;
            margin-bottom: 6px;
            padding-left: 15px;
            position: relative;
        }

        .component-specs li:before {
            content: "•";
            color: #3498db;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .connections-section {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .connections-title {
            font-size: clamp(16px, 3vw, 20px);
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .connection-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: center;
        }

        .flow-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .flow-arrow {
            font-size: 24px;
            color: #3498db;
            margin: 0 10px;
        }

        /* Responsive adjustments for architecture */
        @media (max-width: 767px) {
            .architecture-row {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .system-component {
                padding: 15px;
            }
            .component-icon {
                width: 35px;
                height: 35px;
                font-size: 16px;
            }
            .connections-section {
                padding: 15px;
            }
            .connection-flow {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 768px) and (max-width: 1199px) {
            .architecture-row {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 18px;
            }
            .system-component {
                padding: 18px;
            }
        }

        @media (min-width: 1200px) {
            .architecture-container {
                gap: 25px;
            }
            .architecture-row {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 25px;
            }
            .system-component {
                padding: 20px;
            }
            .connections-section {
                padding: 25px;
                margin: 25px 0;
            }
        }

        /* Modern Scenario Styles */
        .scenario-section {
            background: var(--dark-gradient);
            border-radius: var(--border-radius);
            padding: 40px;
            margin: 40px 0;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-xl);
        }

        .scenario-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            opacity: 0.3;
        }

        .scenario-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .scenario-title {
            font-size: clamp(24px, 4vw, 36px);
            font-weight: 700;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #38ef7d 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .scenario-subtitle {
            font-size: clamp(14px, 2.5vw, 18px);
            opacity: 0.9;
            margin-bottom: 32px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .scenario-controls {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 40px;
            flex-wrap: wrap;
            position: relative;
            z-index: 2;
        }

        .control-btn {
            background: var(--info-gradient);
            border: none;
            color: white;
            padding: 16px 32px;
            border-radius: 50px;
            font-size: clamp(12px, 2vw, 14px);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 16px 48px rgba(79, 172, 254, 0.4);
        }

        .control-btn.active {
            background: var(--warning-gradient);
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.4);
        }

        .warehouse-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 248, 255, 0.95) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-sm);
            padding: 32px;
            position: relative;
            height: 500px;
            overflow: hidden;
            margin-bottom: 32px;
            box-shadow: var(--shadow-xl);
        }

        .warehouse-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(79, 172, 254, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(79, 172, 254, 0.1) 1px, transparent 1px);
            background-size: 30px 30px;
            opacity: 0.6;
        }

        .rfid-reader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            box-shadow: 0 16px 40px rgba(255, 107, 107, 0.4);
            z-index: 10;
            border: 3px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .rfid-reader:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 20px 50px rgba(255, 107, 107, 0.6);
        }

        .coverage-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 350px;
            height: 350px;
            border: 3px solid transparent;
            background: linear-gradient(45deg, #ff6b6b, #4facfe, #38ef7d, #f093fb) border-box;
            border-radius: 50%;
            opacity: 0.7;
            animation: modernPulse 3s infinite;
            background-clip: padding-box;
        }

        .coverage-area::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #ff6b6b, #4facfe, #38ef7d, #f093fb);
            border-radius: 50%;
            z-index: -1;
            animation: rotate 4s linear infinite;
        }

        @keyframes modernPulse {
            0% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
            50% { transform: translate(-50%, -50%) scale(1.08); opacity: 0.5; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .equipment-item {
            position: absolute;
            width: 50px;
            height: 50px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            z-index: 5;
        }

        .equipment-item:hover {
            transform: scale(1.15) translateY(-4px);
            box-shadow: 0 16px 48px rgba(0,0,0,0.3);
        }

        .equipment-safe {
            background: var(--success-gradient);
            box-shadow: 0 8px 32px rgba(17, 153, 142, 0.4);
        }

        .equipment-safe:hover {
            box-shadow: 0 16px 48px rgba(17, 153, 142, 0.6);
        }

        .equipment-alert {
            background: var(--warning-gradient);
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.4);
            animation: modernAlertBlink 2s infinite;
        }

        .equipment-alert:hover {
            box-shadow: 0 16px 48px rgba(240, 147, 251, 0.6);
        }

        .equipment-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
            animation: modernDangerBlink 1.5s infinite;
        }

        .equipment-danger:hover {
            box-shadow: 0 16px 48px rgba(255, 107, 107, 0.6);
        }

        @keyframes modernAlertBlink {
            0%, 50% { opacity: 1; transform: scale(1); }
            25%, 75% { opacity: 0.7; transform: scale(1.05); }
        }

        @keyframes modernDangerBlink {
            0%, 50% { opacity: 1; transform: scale(1); }
            25%, 75% { opacity: 0.5; transform: scale(1.08); }
        }

        .signal-wave {
            position: absolute;
            border: 2px solid #3498db;
            border-radius: 50%;
            opacity: 0;
            animation: signalWave 2s infinite;
        }

        @keyframes signalWave {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                width: 200px;
                height: 200px;
                opacity: 0;
            }
        }

        .status-panel {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-sm);
            padding: 24px;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-lg);
            position: relative;
            z-index: 2;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 20px;
        }

        .status-item {
            text-align: center;
            padding: 20px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-sm);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .status-item:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
        }

        .status-number {
            font-size: clamp(18px, 4vw, 24px);
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .status-label {
            font-size: clamp(10px, 2vw, 12px);
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .moving-equipment {
            animation: moveEquipment 8s infinite linear;
        }

        @keyframes moveEquipment {
            0% { transform: translate(0, 0); }
            25% { transform: translate(100px, -50px); }
            50% { transform: translate(200px, 0); }
            75% { transform: translate(100px, 50px); }
            100% { transform: translate(0, 0); }
        }

        /* Modern Workflow Simulation Styles */
        .workflow-container {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 32px;
            margin-bottom: 32px;
            position: relative;
            z-index: 2;
        }

        .workflow-panel {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-sm);
            padding: 32px;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-lg);
        }

        .workflow-steps {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .workflow-step {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-sm);
            padding: 20px;
            border-left: 4px solid var(--info-gradient);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .workflow-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .workflow-step:hover::before {
            opacity: 1;
        }

        .workflow-step.active {
            background: rgba(240, 147, 251, 0.2);
            border-left: 4px solid var(--warning-gradient);
            transform: translateX(8px);
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }

        .workflow-step.completed {
            background: rgba(17, 153, 142, 0.2);
            border-left: 4px solid var(--success-gradient);
            box-shadow: 0 8px 32px rgba(17, 153, 142, 0.3);
        }

        .step-number {
            display: inline-block;
            width: 32px;
            height: 32px;
            background: var(--info-gradient);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 32px;
            font-size: 14px;
            font-weight: 700;
            margin-right: 16px;
            box-shadow: 0 4px 16px rgba(79, 172, 254, 0.3);
            transition: all 0.3s ease;
        }

        .step-number.active {
            background: var(--warning-gradient);
            animation: modernStepPulse 2s infinite;
            box-shadow: 0 8px 24px rgba(240, 147, 251, 0.5);
        }

        .step-number.completed {
            background: var(--success-gradient);
            box-shadow: 0 4px 16px rgba(17, 153, 142, 0.4);
        }

        @keyframes modernStepPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 8px 24px rgba(240, 147, 251, 0.5); }
            50% { transform: scale(1.15); box-shadow: 0 12px 32px rgba(240, 147, 251, 0.7); }
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: clamp(14px, 2.5vw, 16px);
            color: white;
        }

        .step-description {
            font-size: clamp(11px, 2vw, 13px);
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.4;
        }

        .request-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-label {
            display: block;
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .form-input {
            width: 100%;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 12px;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .delivery-truck {
            position: absolute;
            width: 50px;
            height: 30px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            z-index: 15;
            opacity: 0;
        }

        .delivery-truck.moving {
            animation: deliveryRoute 6s ease-in-out;
        }

        @keyframes deliveryRoute {
            0% {
                opacity: 1;
                left: -60px;
                top: 10%;
            }
            50% {
                opacity: 1;
                left: 50%;
                top: 10%;
            }
            100% {
                opacity: 1;
                left: 50%;
                top: 50%;
            }
        }

        .new-equipment {
            position: absolute;
            width: 35px;
            height: 35px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            font-weight: bold;
            opacity: 0;
            transform: scale(0);
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            border: 3px solid #f39c12;
            animation: newItemAppear 2s ease-out;
        }

        @keyframes newItemAppear {
            0% {
                opacity: 0;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.2) rotate(180deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(360deg);
            }
        }

        .tagging-process {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 3px dashed #f39c12;
            border-radius: 50%;
            opacity: 0;
            animation: taggingAnimation 3s ease-in-out;
        }

        @keyframes taggingAnimation {
            0% {
                opacity: 0;
                transform: scale(0) rotate(0deg);
            }
            25% {
                opacity: 1;
                transform: scale(1) rotate(90deg);
            }
            75% {
                opacity: 1;
                transform: scale(1.1) rotate(270deg);
            }
            100% {
                opacity: 0;
                transform: scale(1) rotate(360deg);
            }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #27ae60);
            width: 0%;
            transition: width 0.5s ease;
        }

        .notification-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            max-height: 150px;
            overflow-y: auto;
        }

        .notification-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 5px;
            font-size: 11px;
            border-left: 3px solid #3498db;
        }

        .notification-item.success {
            border-left-color: #27ae60;
        }

        .notification-item.warning {
            border-left-color: #f39c12;
        }

        .notification-item.error {
            border-left-color: #e74c3c;
        }

        /* Responsive adjustments for workflow */
        @media (max-width: 767px) {
            .workflow-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .workflow-panel {
                padding: 15px;
            }
            .delivery-truck {
                width: 40px;
                height: 25px;
                font-size: 16px;
            }
            .new-equipment {
                width: 30px;
                height: 30px;
                font-size: 14px;
            }
        }

        /* Responsive adjustments for scenario */
        @media (max-width: 767px) {
            .scenario-section {
                padding: 15px;
            }
            .warehouse-container {
                height: 300px;
                padding: 15px;
            }
            .coverage-area {
                width: 200px;
                height: 200px;
            }
            .equipment-item {
                width: 30px;
                height: 30px;
                font-size: 14px;
            }
            .rfid-reader {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <div class="header-content">
                <h1>PROFESSIONAL WAREHOUSE RFID SYSTEM</h1>
                <p>Complete System Architecture & Component Specifications</p>
                <p>Single Reader Solution | 50m Coverage | Compute Module 4 PoE 4G Board</p>

                <div class="header-stats">
                    <div class="stat-item">
                        <div class="stat-number">50m</div>
                        <div class="stat-label">Coverage Radius</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1</div>
                        <div class="stat-label">RFID Reader</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">30+</div>
                        <div class="stat-label">Equipment Tags</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Monitoring</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-wrapper">
            <!-- Materials Section -->
            <div class="materials-section">
                <div class="section-title">🔧 System Components & Real Images</div>

                <div class="materials-grid">
                    <!-- Compute Module 4 PoE 4G Board -->
                    <div class="material-card">
                        <img src="https://www.waveshare.com/media/catalog/product/cache/1/image/560x560/9df78eab33525d08d6e5fb8d27136e95/c/o/compute-module-4-poe-4g-board-1.jpg"
                             alt="CM4 PoE 4G Board" class="material-image">
                        <div class="material-title">Compute Module 4 PoE 4G Board</div>
                        <div class="material-model">Waveshare CM4-PoE-4G-Board</div>
                        <div class="material-qty">QTY: 1</div>
                        <div class="material-purpose">Integrated processing unit combining CM4 (4GB RAM, 32GB eMMC) with PoE power and 4G connectivity for complete system control</div>
                        <div class="material-specs">• SIM7600G-H 4G module • PoE 802.3af/at • Ethernet Gigabit • GPS positioning • Industrial grade</div>
                    </div>

                    <!-- PoE Switch -->
                    <div class="material-card">
                        <img src="https://www.netgear.com/cid/fit/2048x1265/to/jpg/https/www.netgear.com/media/B3-gs308pp_32_tcm148-140435.png"
                             alt="NETGEAR PoE Switch" class="material-image">
                        <div class="material-title">NETGEAR PoE Switch</div>
                        <div class="material-model">GS308PP-100NAS</div>
                        <div class="material-qty">QTY: 1</div>
                        <div class="material-purpose">Central network hub providing power and data connectivity to RFID reader and CM4 board</div>
                        <div class="material-specs">• 8-port Gigabit Ethernet • 120W PoE+ budget • Fanless design • Auto-sensing PoE</div>
                    </div>

                    <!-- RFID Reader -->
                    <div class="material-card">
                        <img src="https://support.impinj.com/hc/article_attachments/360008274634/R420_Front_Angle.png"
                             alt="Impinj R420 Reader" class="material-image">
                        <div class="material-title">Impinj Speedway R420</div>
                        <div class="material-model">IPJ-REV-R420-USA001M</div>
                        <div class="material-qty">QTY: 1</div>
                        <div class="material-purpose">High-performance UHF RFID reader with extended 50m range for complete warehouse coverage</div>
                        <div class="material-specs">• 4 antenna ports • 902-928 MHz (US) • 50m read range • PoE powered • Advanced signal processing</div>
                    </div>

                    <!-- High-Gain Antennas -->
                    <div class="material-card">
                        <img src="https://www.lairdconnect.com/sites/default/files/2019-09/S9028PCL_0.jpg"
                             alt="Laird Antenna" class="material-image">
                        <div class="material-title">Laird High-Gain Antennas</div>
                        <div class="material-model">S9028PCL</div>
                        <div class="material-qty">QTY: 4</div>
                        <div class="material-purpose">Circular polarized antennas with 12 dBi gain for extended 50m range coverage in industrial environment</div>
                        <div class="material-specs">• 902-928 MHz frequency • 12 dBi gain • Circular polarization • IP67 weatherproof • 50m effective range</div>
                    </div>

                    <!-- Metal RFID Tags -->
                    <div class="material-card">
                        <img src="https://xerafy.com/wp-content/uploads/2019/05/Xerafy-Dot-XS-RFID-Tag.jpg"
                             alt="Xerafy Metal Tags" class="material-image">
                        <div class="material-title">Xerafy Metal RFID Tags</div>
                        <div class="material-model">Xerafy Dot XS</div>
                        <div class="material-qty">QTY: 20</div>
                        <div class="material-purpose">Industrial-grade tags for metal equipment (tools, machinery) with strong adhesive mounting</div>
                        <div class="material-specs">• UHF 860-960 MHz • Read range 0-50m • IP68 rated • Operating temp: -40°C to +85°C</div>
                    </div>

                    <!-- Non-Metal RFID Tags -->
                    <div class="material-card">
                        <img src="https://www.averydennison.com/-/media/averydennison/smartrac/images/products/inlays-tags/dogbone/ad-160u7.jpg"
                             alt="Avery Dennison Tags" class="material-image">
                        <div class="material-title">Avery Dennison Tags</div>
                        <div class="material-model">AD-160u7</div>
                        <div class="material-qty">QTY: 10</div>
                        <div class="material-purpose">Standard UHF tags for non-metal equipment (pallets, containers) with excellent read performance</div>
                        <div class="material-specs">• UHF 860-960 MHz • Read range 0-50m • Adhesive mounting • Flexible substrate</div>
                    </div>

                    <!-- CAT6 Cables -->
                    <div class="material-card">
                        <img src="https://m.media-amazon.com/images/I/61VnQyHdBuL._AC_SL1500_.jpg"
                             alt="CAT6 Cable" class="material-image">
                        <div class="material-title">Shielded CAT6 Cables</div>
                        <div class="material-model">CAT6-SFTP-2M</div>
                        <div class="material-qty">QTY: 2</div>
                        <div class="material-purpose">High-quality Ethernet cables for PoE power delivery and data transmission (reduced quantity due to single reader)</div>
                        <div class="material-specs">• Category 6 shielded • 2 meter length • RJ45 connectors • 1Gbps data rate</div>
                    </div>

                    <!-- Industrial Enclosure -->
                    <div class="material-card">
                        <img src="https://www.waveshare.com/media/catalog/product/cache/1/image/560x560/9df78eab33525d08d6e5fb8d27136e95/c/m/cm4-io-base-box-b-1.jpg"
                             alt="Industrial Enclosure" class="material-image">
                        <div class="material-title">Industrial Enclosure</div>
                        <div class="material-model">CM4-IO-BASE-BOX-B</div>
                        <div class="material-qty">QTY: 1</div>
                        <div class="material-purpose">Industrial-grade protective enclosure for CM4 and 4G board against dust, moisture, and impacts</div>
                        <div class="material-specs">• IP65 protection rating • Aluminum construction • Heat dissipation fins • Cable glands</div>
                    </div>
                </div>
            </div>

            <!-- Professional System Architecture -->
            <div class="diagram-section">
                <div class="section-title">🏗️ Professional System Architecture</div>

                <div class="architecture-container">
                    <!-- Power Infrastructure -->
                    <div class="architecture-card">
                        <div style="display: flex; align-items: center; margin-bottom: 24px;">
                            <div style="width: 60px; height: 60px; background: var(--warning-gradient); border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 20px;">⚡</div>
                            <div>
                                <h3 style="margin: 0; font-size: clamp(18px, 3vw, 24px); font-weight: 700; color: var(--text-primary);">Power Infrastructure</h3>
                                <p style="margin: 4px 0 0 0; color: var(--text-secondary); font-size: clamp(12px, 2vw, 16px);">AC Power Distribution</p>
                            </div>
                        </div>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #38ef7d; font-weight: bold;">•</span>
                                220V/110V AC Power Supply
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #38ef7d; font-weight: bold;">•</span>
                                Dedicated 15A circuit
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #38ef7d; font-weight: bold;">•</span>
                                Surge protection included
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #38ef7d; font-weight: bold;">•</span>
                                UPS compatible design
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #38ef7d; font-weight: bold;">•</span>
                                150W total consumption
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #38ef7d; font-weight: bold;">•</span>
                                Industrial grade reliability
                            </li>
                        </ul>
                    </div>

                    <!-- Network Infrastructure -->
                    <div class="architecture-card">
                        <div style="display: flex; align-items: center; margin-bottom: 24px;">
                            <div style="width: 60px; height: 60px; background: var(--info-gradient); border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 20px;">🌐</div>
                            <div>
                                <h3 style="margin: 0; font-size: clamp(18px, 3vw, 24px); font-weight: 700; color: var(--text-primary);">Network Infrastructure</h3>
                                <p style="margin: 4px 0 0 0; color: var(--text-secondary); font-size: clamp(12px, 2vw, 16px);">NETGEAR GS308PP PoE Switch</p>
                            </div>
                        </div>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #4facfe; font-weight: bold;">•</span>
                                8-port Gigabit Ethernet
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #4facfe; font-weight: bold;">•</span>
                                120W PoE+ budget
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #4facfe; font-weight: bold;">•</span>
                                Auto-sensing PoE delivery
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #4facfe; font-weight: bold;">•</span>
                                Fanless silent operation
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #4facfe; font-weight: bold;">•</span>
                                Central network hub
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #4facfe; font-weight: bold;">•</span>
                                Plug-and-play setup
                            </li>
                        </ul>
                    </div>

                    <!-- Processing & Connectivity -->
                    <div class="architecture-card">
                        <div style="display: flex; align-items: center; margin-bottom: 24px;">
                            <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 20px;">🧠</div>
                            <div>
                                <h3 style="margin: 0; font-size: clamp(18px, 3vw, 24px); font-weight: 700; color: var(--text-primary);">Processing & Connectivity</h3>
                                <p style="margin: 4px 0 0 0; color: var(--text-secondary); font-size: clamp(12px, 2vw, 16px);">CM4 PoE 4G Board (Waveshare)</p>
                            </div>
                        </div>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #667eea; font-weight: bold;">•</span>
                                4GB RAM / 32GB eMMC
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #667eea; font-weight: bold;">•</span>
                                SIM7600G-H 4G module
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #667eea; font-weight: bold;">•</span>
                                Real-time data processing
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #667eea; font-weight: bold;">•</span>
                                Web dashboard hosting
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #667eea; font-weight: bold;">•</span>
                                Alert management system
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #667eea; font-weight: bold;">•</span>
                                GPS positioning capability
                            </li>
                        </ul>
                    </div>

                    <!-- RFID Tracking System -->
                    <div class="architecture-card">
                        <div style="display: flex; align-items: center; margin-bottom: 24px;">
                            <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #ff6b6b, #ee5a24); border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 20px;">📡</div>
                            <div>
                                <h3 style="margin: 0; font-size: clamp(18px, 3vw, 24px); font-weight: 700; color: var(--text-primary);">RFID Tracking System</h3>
                                <p style="margin: 4px 0 0 0; color: var(--text-secondary); font-size: clamp(12px, 2vw, 16px);">Impinj R420 + 4 High-Gain Antennas</p>
                            </div>
                        </div>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #ff6b6b; font-weight: bold;">•</span>
                                Single reader solution
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #ff6b6b; font-weight: bold;">•</span>
                                50m coverage radius
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #ff6b6b; font-weight: bold;">•</span>
                                4 Laird S9028PCL antennas
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #ff6b6b; font-weight: bold;">•</span>
                                12 dBi high gain
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #ff6b6b; font-weight: bold;">•</span>
                                902-928 MHz (US frequency)
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #ff6b6b; font-weight: bold;">•</span>
                                30+ simultaneous tags
                            </li>
                        </ul>
                    </div>

                    <!-- Cellular Connectivity -->
                    <div class="architecture-card">
                        <div style="display: flex; align-items: center; margin-bottom: 24px;">
                            <div style="width: 60px; height: 60px; background: var(--success-gradient); border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 20px;">📶</div>
                            <div>
                                <h3 style="margin: 0; font-size: clamp(18px, 3vw, 24px); font-weight: 700; color: var(--text-primary);">Cellular Connectivity</h3>
                                <p style="margin: 4px 0 0 0; color: var(--text-secondary); font-size: clamp(12px, 2vw, 16px);">4G/LTE Remote Access</p>
                            </div>
                        </div>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #11998e; font-weight: bold;">•</span>
                                4G/LTE cellular connection
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #11998e; font-weight: bold;">•</span>
                                Remote dashboard access
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #11998e; font-weight: bold;">•</span>
                                SMS/Email alert system
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #11998e; font-weight: bold;">•</span>
                                10GB monthly data plan
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #11998e; font-weight: bold;">•</span>
                                External high-gain antenna
                            </li>
                            <li style="margin-bottom: 12px; padding-left: 20px; position: relative; font-size: clamp(12px, 2vw, 14px); color: var(--text-primary);">
                                <span style="position: absolute; left: 0; color: #11998e; font-weight: bold;">•</span>
                                Real-time monitoring
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- System Connections Flow -->
                <div class="connections-section">
                    <h3 class="connections-title">🔗 System Connection Flow</h3>
                    <div class="connection-flow">
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">⚡</div>
                            <strong>AC Power</strong><br>
                            <small>220V/110V Supply</small>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">🌐</div>
                            <strong>PoE Switch</strong><br>
                            <small>Power + Data Hub</small>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">🧠</div>
                            <strong>CM4 Board</strong><br>
                            <small>Processing Unit</small>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">📶</div>
                            <strong>4G Network</strong><br>
                            <small>Remote Access</small>
                        </div>
                    </div>

                    <div class="connection-flow" style="margin-top: 20px;">
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">🌐</div>
                            <strong>PoE Switch</strong><br>
                            <small>Network Hub</small>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">📡</div>
                            <strong>RFID Reader</strong><br>
                            <small>Impinj R420</small>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">📶</div>
                            <strong>4 Antennas</strong><br>
                            <small>50m Coverage</small>
                        </div>
                        <div class="flow-arrow">→</div>
                        <div class="flow-item">
                            <div style="font-size: 20px; margin-bottom: 8px;">🏷️</div>
                            <strong>RFID Tags</strong><br>
                            <small>Equipment Tracking</small>
                        </div>
                    </div>
                </div>

                <!-- System Performance Summary -->
                <div class="connections-section">
                    <h3 class="connections-title">📊 System Performance & Specifications</h3>
                    <div class="architecture-row">
                        <div class="system-component" style="border-left-color: #e74c3c;">
                            <div class="component-header">
                                <div class="component-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">⚡</div>
                                <div>
                                    <h4 class="component-title">Power Requirements</h4>
                                </div>
                            </div>
                            <ul class="component-specs">
                                <li>150W total consumption</li>
                                <li>15A dedicated circuit</li>
                                <li>UPS compatible</li>
                                <li>Surge protection</li>
                            </ul>
                        </div>

                        <div class="system-component" style="border-left-color: #e67e22;">
                            <div class="component-header">
                                <div class="component-icon" style="background: linear-gradient(135deg, #e67e22, #d35400);">📡</div>
                                <div>
                                    <h4 class="component-title">RFID Performance</h4>
                                </div>
                            </div>
                            <ul class="component-specs">
                                <li>50m coverage radius</li>
                                <li>2-5m location accuracy</li>
                                <li>30+ simultaneous tags</li>
                                <li>Real-time updates</li>
                            </ul>
                        </div>

                        <div class="system-component" style="border-left-color: #27ae60;">
                            <div class="component-header">
                                <div class="component-icon" style="background: linear-gradient(135deg, #27ae60, #229954);">📶</div>
                                <div>
                                    <h4 class="component-title">Connectivity</h4>
                                </div>
                            </div>
                            <ul class="component-specs">
                                <li>4G/LTE cellular</li>
                                <li>Remote dashboard</li>
                                <li>SMS/Email alerts</li>
                                <li>10GB data plan</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Advanced Hardware Integration Simulation -->
                <div class="scenario-section">
                    <div class="scenario-header">
                        <h2 class="scenario-title">� Industrial Manufacturing Plant Asset Management</h2>
                        <p class="scenario-subtitle">Complete Hardware Integration: CM4 PoE 4G Board → RFID System → Real-Time Asset Tracking</p>

                        <div class="scenario-controls">
                            <button class="control-btn" id="startWorkflowBtn" onclick="startCompleteWorkflow()">🚀 Start Plant Demo</button>
                            <button class="control-btn" id="startTrackingBtn" onclick="startTrackingOnly()">📡 Live Monitoring</button>
                            <button class="control-btn" id="hardwareBtn" onclick="showHardwareSetup()">🔧 Hardware View</button>
                            <button class="control-btn" id="pauseBtn" onclick="pauseDemo()">⏸️ Pause</button>
                            <button class="control-btn" id="resetBtn" onclick="resetDemo()">🔄 Reset</button>
                            <button class="control-btn" id="alertBtn" onclick="triggerAlert()">🚨 Asset Alert</button>
                        </div>
                    </div>

                    <!-- Complete Workflow Container -->
                    <div class="workflow-container">
                        <!-- Left Panel: Workflow Steps -->
                        <div class="workflow-panel">
                            <h3 style="margin-top: 0; margin-bottom: 15px;">📋 Manufacturing Asset Workflow</h3>

                            <!-- Asset Request Form -->
                            <div class="request-form" id="requestForm">
                                <h4 style="margin: 0 0 10px 0;">📝 New Asset Request</h4>
                                <div class="form-group">
                                    <label class="form-label">Asset Type:</label>
                                    <input type="text" class="form-input" id="equipmentType" placeholder="e.g., CNC Machine" value="CNC Machine">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Quantity:</label>
                                    <input type="number" class="form-input" id="quantity" placeholder="1" value="1">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Requested By:</label>
                                    <input type="text" class="form-input" id="requestedBy" placeholder="Production Manager" value="Production Manager">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Department:</label>
                                    <input type="text" class="form-input" id="department" placeholder="Manufacturing" value="Manufacturing">
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>

                            <!-- Workflow Steps -->
                            <div class="workflow-steps">
                                <div class="workflow-step" id="step1">
                                    <span class="step-number" id="stepNum1">1</span>
                                    <div>
                                        <div class="step-title">Asset Request Submitted</div>
                                        <div class="step-description">Production manager submits new asset request</div>
                                    </div>
                                </div>

                                <div class="workflow-step" id="step2">
                                    <span class="step-number" id="stepNum2">2</span>
                                    <div>
                                        <div class="step-title">Hardware System Setup</div>
                                        <div class="step-description">CM4 PoE 4G Board and RFID reader configured</div>
                                    </div>
                                </div>

                                <div class="workflow-step" id="step3">
                                    <span class="step-number" id="stepNum3">3</span>
                                    <div>
                                        <div class="step-title">Asset Delivered to Plant</div>
                                        <div class="step-description">Manufacturing equipment arrives at facility</div>
                                    </div>
                                </div>

                                <div class="workflow-step" id="step4">
                                    <span class="step-number" id="stepNum4">4</span>
                                    <div>
                                        <div class="step-title">RFID Tag Integration</div>
                                        <div class="step-description">Industrial RFID tags applied to asset</div>
                                    </div>
                                </div>

                                <div class="workflow-step" id="step5">
                                    <span class="step-number" id="stepNum5">5</span>
                                    <div>
                                        <div class="step-title">System Registration</div>
                                        <div class="step-description">Asset registered in manufacturing database</div>
                                    </div>
                                </div>

                                <div class="workflow-step" id="step6">
                                    <span class="step-number" id="stepNum6">6</span>
                                    <div>
                                        <div class="step-title">Live Asset Monitoring</div>
                                        <div class="step-description">24/7 tracking via 4G connectivity active</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Panel -->
                            <div class="notification-panel" id="notificationPanel">
                                <div class="notification-item">
                                    <strong>System Ready:</strong> Waiting for workflow to start...
                                </div>
                            </div>
                        </div>

                        <!-- Right Panel: Visual Simulation -->
                        <div class="workflow-panel">
                            <h3 style="margin-top: 0; margin-bottom: 15px;">🏭 Manufacturing Plant Visualization</h3>

                            <!-- Manufacturing Plant Simulation -->
                            <div class="warehouse-container" id="warehouseContainer">
                                <div class="warehouse-grid"></div>

                                <!-- Hardware Components -->
                                <!-- CM4 PoE 4G Board (Control Center) -->
                                <div class="equipment-item equipment-safe" id="cm4Board" style="top: 5%; left: 5%; background: linear-gradient(135deg, #9b59b6, #8e44ad);" title="CM4 PoE 4G Board - Control Center">🧠</div>

                                <!-- PoE Switch -->
                                <div class="equipment-item equipment-safe" id="poeSwitch" style="top: 5%; left: 20%; background: linear-gradient(135deg, #3498db, #2980b9);" title="NETGEAR PoE Switch - Network Hub">🌐</div>

                                <!-- Industrial Enclosure -->
                                <div class="equipment-item equipment-safe" id="enclosure" style="top: 5%; left: 35%; background: linear-gradient(135deg, #95a5a6, #7f8c8d);" title="Industrial Enclosure - Protection">🏠</div>

                                <!-- Delivery Truck (will appear during delivery) -->
                                <div class="delivery-truck" id="deliveryTruck">🚚</div>

                                <!-- RFID Reader (Center) -->
                                <div class="rfid-reader" id="rfidReader" title="Impinj R420 RFID Reader">📡</div>

                                <!-- High-Gain Antennas (4 corners) -->
                                <div class="equipment-item equipment-safe" id="antenna1" style="top: 15%; left: 85%; background: linear-gradient(135deg, #e67e22, #d35400);" title="Laird High-Gain Antenna 1">📶</div>
                                <div class="equipment-item equipment-safe" id="antenna2" style="top: 85%; left: 85%; background: linear-gradient(135deg, #e67e22, #d35400);" title="Laird High-Gain Antenna 2">📶</div>
                                <div class="equipment-item equipment-safe" id="antenna3" style="top: 85%; left: 15%; background: linear-gradient(135deg, #e67e22, #d35400);" title="Laird High-Gain Antenna 3">📶</div>
                                <div class="equipment-item equipment-safe" id="antenna4" style="top: 15%; left: 15%; background: linear-gradient(135deg, #e67e22, #d35400);" title="Laird High-Gain Antenna 4">📶</div>

                                <!-- Coverage Area -->
                                <div class="coverage-area" id="coverageArea"></div>

                                <!-- Tagging Process Animation (will appear during tagging) -->
                                <div class="tagging-process" id="taggingProcess" style="top: 50%; left: 50%; transform: translate(-50%, -50%);"></div>

                                <!-- Manufacturing Assets -->
                                <div class="equipment-item equipment-safe" id="cncMachine" style="top: 25%; left: 25%;" title="CNC Machine - Safe Zone">⚙️</div>
                                <div class="equipment-item equipment-safe" id="roboticArm" style="top: 35%; left: 65%;" title="Robotic Arm - Safe Zone">🦾</div>
                                <div class="equipment-item equipment-alert" id="conveyor" style="top: 65%; left: 35%;" title="Conveyor Belt - Alert Zone">�</div>
                                <div class="equipment-item equipment-safe" id="lathe" style="top: 55%; left: 70%;" title="Industrial Lathe - Safe Zone">�</div>
                                <div class="equipment-item equipment-danger" id="missing" style="top: 10%; left: 90%;" title="Missing Asset - Out of Range">❌</div>
                                <div class="equipment-item equipment-safe moving-equipment" id="agv" style="top: 75%; left: 15%;" title="Automated Guided Vehicle - Moving">🤖</div>

                                <!-- New Asset (will appear after delivery) -->
                                <div class="new-equipment" id="newEquipment" style="top: 50%; left: 50%; display: none;" title="New CNC Machine">⚙️</div>

                                <!-- Signal Waves (will be added dynamically) -->
                                <div class="signal-wave" id="signalWave1" style="top: 50%; left: 50%; transform: translate(-50%, -50%);"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Real-time Status Panel -->
                    <div class="status-panel">
                        <div class="status-grid">
                            <div class="status-item">
                                <div class="status-number" id="totalEquipment" style="color: #3498db;">6</div>
                                <div class="status-label">Total Equipment</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number" id="safeEquipment" style="color: #27ae60;">4</div>
                                <div class="status-label">Safe Zone</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number" id="alertEquipment" style="color: #f39c12;">1</div>
                                <div class="status-label">Alert Zone</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number" id="missingEquipment" style="color: #e74c3c;">1</div>
                                <div class="status-label">Out of Range</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number" id="systemStatus" style="color: #27ae60;">ONLINE</div>
                                <div class="status-label">System Status</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number" id="lastUpdate" style="color: #3498db;">LIVE</div>
                                <div class="status-label">Last Update</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let demoRunning = false;
        let workflowRunning = false;
        let currentStep = 0;
        let demoInterval;
        let signalInterval;
        let updateInterval;
        let workflowInterval;

        // Complete Workflow Simulation
        function startCompleteWorkflow() {
            if (workflowRunning) return;

            resetDemo();
            workflowRunning = true;
            currentStep = 0;

            document.getElementById('startWorkflowBtn').classList.add('active');
            document.getElementById('startWorkflowBtn').innerHTML = '⏹️ Stop Workflow';

            addNotification('🚀 Starting complete workflow simulation...', 'success');

            // Start the workflow steps
            executeWorkflowStep(1);
        }

        function executeWorkflowStep(step) {
            if (!workflowRunning) return;

            currentStep = step;
            updateProgress((step / 6) * 100);

            // Mark current step as active
            for (let i = 1; i <= 6; i++) {
                const stepEl = document.getElementById(`step${i}`);
                const stepNumEl = document.getElementById(`stepNum${i}`);

                if (i < step) {
                    stepEl.className = 'workflow-step completed';
                    stepNumEl.className = 'step-number completed';
                } else if (i === step) {
                    stepEl.className = 'workflow-step active';
                    stepNumEl.className = 'step-number active';
                } else {
                    stepEl.className = 'workflow-step';
                    stepNumEl.className = 'step-number';
                }
            }

            switch(step) {
                case 1:
                    addNotification('📝 Material request submitted by ' + document.getElementById('requestedBy').value, 'success');
                    setTimeout(() => executeWorkflowStep(2), 2000);
                    break;

                case 2:
                    addNotification('✅ Request approved by manager and order placed', 'success');
                    setTimeout(() => executeWorkflowStep(3), 2500);
                    break;

                case 3:
                    addNotification('🚚 Equipment delivery in progress...', 'warning');
                    startDeliveryAnimation();
                    setTimeout(() => executeWorkflowStep(4), 6000);
                    break;

                case 4:
                    addNotification('🏷️ Applying RFID tag to new equipment...', 'warning');
                    startTaggingProcess();
                    setTimeout(() => executeWorkflowStep(5), 4000);
                    break;

                case 5:
                    addNotification('💾 Registering equipment in tracking database...', 'warning');
                    registerNewEquipment();
                    setTimeout(() => executeWorkflowStep(6), 3000);
                    break;

                case 6:
                    addNotification('📡 Real-time tracking activated! Equipment now monitored 24/7', 'success');
                    startTrackingSystem();
                    break;
            }
        }

        function startDeliveryAnimation() {
            const truck = document.getElementById('deliveryTruck');
            truck.classList.add('moving');

            setTimeout(() => {
                truck.classList.remove('moving');
                addNotification('📦 Equipment delivered to warehouse', 'success');
            }, 6000);
        }

        function startTaggingProcess() {
            const taggingEl = document.getElementById('taggingProcess');
            const newEquipEl = document.getElementById('newEquipment');

            // Show new equipment
            newEquipEl.style.display = 'flex';
            newEquipEl.style.opacity = '1';

            // Start tagging animation
            taggingEl.style.opacity = '1';
            taggingEl.style.animation = 'taggingAnimation 3s ease-in-out';

            setTimeout(() => {
                taggingEl.style.opacity = '0';
                addNotification('🏷️ RFID tag successfully applied', 'success');
            }, 3000);
        }

        function registerNewEquipment() {
            const equipmentType = document.getElementById('equipmentType').value;
            addNotification(`💾 ${equipmentType} registered with ID: EQ-${Date.now().toString().slice(-4)}`, 'success');

            // Update status counters
            const totalEl = document.getElementById('totalEquipment');
            const safeEl = document.getElementById('safeEquipment');

            totalEl.textContent = parseInt(totalEl.textContent) + 1;
            safeEl.textContent = parseInt(safeEl.textContent) + 1;
        }

        function startTrackingSystem() {
            // Convert new equipment to tracked equipment
            const newEquipEl = document.getElementById('newEquipment');
            newEquipEl.className = 'equipment-item equipment-safe';
            newEquipEl.style.border = '2px solid #27ae60';

            // Start tracking demo
            startTrackingOnly();

            addNotification('✅ Workflow complete! Equipment is now being tracked in real-time.', 'success');
        }

        function startTrackingOnly() {
            if (demoRunning) return;

            demoRunning = true;
            document.getElementById('startTrackingBtn').classList.add('active');
            document.getElementById('startTrackingBtn').innerHTML = '⏹️ Stop Tracking';

            // Start signal waves
            signalInterval = setInterval(createSignalWave, 1000);

            // Start status updates
            updateInterval = setInterval(updateStatus, 2000);

            // Add movement to equipment
            document.getElementById('forklift').style.animation = 'moveEquipment 8s infinite linear';

            // Simulate real-time updates
            simulateRealTimeUpdates();

            addNotification('📡 Real-time tracking system activated', 'success');
        }

        function pauseDemo() {
            demoRunning = false;
            workflowRunning = false;

            document.getElementById('startWorkflowBtn').classList.remove('active');
            document.getElementById('startWorkflowBtn').innerHTML = '🚀 Start Complete Workflow';
            document.getElementById('startTrackingBtn').classList.remove('active');
            document.getElementById('startTrackingBtn').innerHTML = '📡 Tracking Only';

            clearInterval(signalInterval);
            clearInterval(updateInterval);
            clearInterval(demoInterval);
            clearInterval(workflowInterval);

            addNotification('⏸️ Simulation paused', 'warning');
        }

        function resetDemo() {
            pauseDemo();

            // Reset workflow steps
            for (let i = 1; i <= 6; i++) {
                document.getElementById(`step${i}`).className = 'workflow-step';
                document.getElementById(`stepNum${i}`).className = 'step-number';
            }

            // Reset progress bar
            updateProgress(0);

            // Reset all equipment to original positions and states
            document.getElementById('drill').className = 'equipment-item equipment-safe';
            document.getElementById('hammer').className = 'equipment-item equipment-safe';
            document.getElementById('pallet').className = 'equipment-item equipment-alert';
            document.getElementById('wrench').className = 'equipment-item equipment-safe';
            document.getElementById('missing').className = 'equipment-item equipment-danger';
            document.getElementById('forklift').className = 'equipment-item equipment-safe moving-equipment';
            document.getElementById('forklift').style.animation = 'none';

            // Hide new equipment
            document.getElementById('newEquipment').style.display = 'none';
            document.getElementById('newEquipment').style.opacity = '0';

            // Reset delivery truck
            document.getElementById('deliveryTruck').classList.remove('moving');

            // Reset tagging process
            document.getElementById('taggingProcess').style.opacity = '0';

            // Reset status
            document.getElementById('totalEquipment').textContent = '6';
            document.getElementById('safeEquipment').textContent = '4';
            document.getElementById('alertEquipment').textContent = '1';
            document.getElementById('missingEquipment').textContent = '1';
            document.getElementById('systemStatus').textContent = 'ONLINE';
            document.getElementById('systemStatus').style.color = '#27ae60';

            // Clear signal waves
            const waves = document.querySelectorAll('.signal-wave');
            waves.forEach(wave => {
                wave.style.animation = 'none';
            });

            // Clear notifications
            clearNotifications();
            addNotification('🔄 System reset complete', 'success');
        }

        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function addNotification(message, type) {
            const panel = document.getElementById('notificationPanel');
            const notification = document.createElement('div');
            notification.className = `notification-item ${type}`;
            notification.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;

            // Add to top of panel
            panel.insertBefore(notification, panel.firstChild);

            // Keep only last 5 notifications
            while (panel.children.length > 5) {
                panel.removeChild(panel.lastChild);
            }

            // Scroll to top
            panel.scrollTop = 0;
        }

        function clearNotifications() {
            const panel = document.getElementById('notificationPanel');
            panel.innerHTML = '<div class="notification-item"><strong>System Ready:</strong> Waiting for workflow to start...</div>';
        }

        function triggerAlert() {
            // Simulate equipment going out of range
            const drill = document.getElementById('drill');
            drill.className = 'equipment-item equipment-danger';

            // Update status
            document.getElementById('safeEquipment').textContent = '3';
            document.getElementById('missingEquipment').textContent = '2';
            document.getElementById('systemStatus').textContent = 'ALERT';
            document.getElementById('systemStatus').style.color = '#e74c3c';

            // Show alert notification
            showNotification('🚨 ALERT: Drill Set has moved out of range!', 'danger');

            // Reset after 3 seconds
            setTimeout(() => {
                drill.className = 'equipment-item equipment-safe';
                document.getElementById('safeEquipment').textContent = '4';
                document.getElementById('missingEquipment').textContent = '1';
                document.getElementById('systemStatus').textContent = 'ONLINE';
                document.getElementById('systemStatus').style.color = '#27ae60';
                showNotification('✅ Equipment back in safe zone', 'success');
            }, 3000);
        }

        function createSignalWave() {
            const reader = document.getElementById('rfidReader');
            const wave = document.createElement('div');
            wave.className = 'signal-wave';
            wave.style.position = 'absolute';
            wave.style.top = '50%';
            wave.style.left = '50%';
            wave.style.transform = 'translate(-50%, -50%)';
            wave.style.border = '2px solid #3498db';
            wave.style.borderRadius = '50%';
            wave.style.animation = 'signalWave 2s ease-out';

            document.getElementById('warehouseContainer').appendChild(wave);

            // Remove wave after animation
            setTimeout(() => {
                if (wave.parentNode) {
                    wave.parentNode.removeChild(wave);
                }
            }, 2000);
        }

        function updateStatus() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('lastUpdate').textContent = timeString;
        }

        function simulateRealTimeUpdates() {
            demoInterval = setInterval(() => {
                // Randomly change equipment status to simulate real tracking
                const equipment = ['drill', 'hammer', 'wrench'];
                const randomEquip = equipment[Math.floor(Math.random() * equipment.length)];
                const element = document.getElementById(randomEquip);

                // Temporarily change to alert state
                const originalClass = element.className;
                element.className = 'equipment-item equipment-alert';

                // Change back after 1 second
                setTimeout(() => {
                    element.className = originalClass;
                }, 1000);

            }, 5000);
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'danger' ? '#e74c3c' : '#27ae60'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                font-weight: bold;
                z-index: 1000;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add CSS for notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Auto-start demo when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                showNotification('🎬 Complete workflow simulation ready! Choose "Start Complete Workflow" for full demo.', 'success');
                addNotification('🚀 System initialized and ready for demonstration', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
